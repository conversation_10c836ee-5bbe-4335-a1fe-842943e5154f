import { FormioSchema, FormioComponent, FormioColumnsComponent, FormioColumn } from './types';

/**
 * Injects a phone number into the Formio schema by setting the phone field to disabled and pre-populated.
 * Can be extended for other data injections in the future.
 */
export const injectPhoneNumberIntoSchema = (
  schema: FormioSchema,
  phoneNumber: string,
): FormioSchema => {
  if (!schema || !schema.components) return schema;

  const transformedSchema: FormioSchema = JSON.parse(JSON.stringify(schema)); // Deep clone

  const findAndModifyPhoneField = (components: Array<FormioComponent | FormioColumnsComponent>) => {
    components.forEach((component) => {
      if ('key' in component && component.key === 'phone') {
        // Make phone field disabled and pre-populate it
        (component as FormioComponent).disabled = true;
        (component as FormioComponent).defaultValue = phoneNumber;
        // Optionally remove required validation since it's pre-filled
        if ((component as FormioComponent).validate) {
          (component as FormioComponent).validate!.required = false;
        }
      }
      // Handle nested components (like columns)
      if ('components' in component && Array.isArray(component.components)) {
        findAndModifyPhoneField(component.components);
      }
      if ('columns' in component && Array.isArray(component.columns)) {
        (component as FormioColumnsComponent).columns.forEach((column: FormioColumn) => {
          if (Array.isArray(column.components)) {
            findAndModifyPhoneField(column.components);
          }
        });
      }
    });
  };

  findAndModifyPhoneField(transformedSchema.components);
  return transformedSchema;
};
