import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { OTPRequestPayload, OTPResponse } from '@/types/otp.types';
import { useMutation } from '@tanstack/react-query';

export const useRequestOTP = () => {
  return useMutation({
    mutationFn: (payload: OTPRequestPayload) => requestOTP(payload),
  });
};

export async function requestOTP(payload: OTPRequestPayload): Promise<OTPResponse> {
  try {
    const response = await apiClient.post('/api/v1/users/otp', payload);
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to request OTP');
  }
}
