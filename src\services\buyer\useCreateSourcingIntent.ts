import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { SourcingIntentPayload } from '@/types/sourcingItent.types';
import { useMutation } from '@tanstack/react-query';

export async function createSourcingIntent(
  payload: SourcingIntentPayload,
): Promise<{ message: string }> {
  try {
    const response = await apiClient.post<{ message: string }>(
      '/api/v1/users/buyer/sourcing-intent/create/',
      payload,
    );
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'An unknown error occurred.');
  }
}

export const useSourcingIntentAPI = () => {
  return useMutation({
    mutationFn: (payload: SourcingIntentPayload) => createSourcingIntent(payload),
  });
};
