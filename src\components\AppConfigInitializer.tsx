'use client';
import useAppConfig from '@/hooks/useAppConfig';
import { useConfigStore } from '@/store/useAppConfig.store';
import { useEffect } from 'react';

interface AppConfigInitializerProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const AppConfigInitializer = ({ children }: AppConfigInitializerProps) => {
  const { isLoading, error, isError } = useAppConfig();
  const { isConfigLoaded, isConfigLoading } = useConfigStore();

  useEffect(() => {
    if (isError) {
      console.error('Failed to load app configuration:', error);
    }
  }, [isError, error]);

  // Show loading state while config is being loaded
  // TODO: maybe update this to a spinner or loader later
  if (isLoading || isConfigLoading || !isConfigLoaded) {
    return (
      <div className="w-full min-h-screen flex items-center justify-center">
        Loading configuration...
      </div>
    );
  }

  // Show error state if config failed to load
  if (isError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-lg font-semibold text-red-600 mb-2">Configuration Error</h2>
          <p className="text-gray-600 mb-4">
            Failed to load application configuration. Please refresh the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AppConfigInitializer;
