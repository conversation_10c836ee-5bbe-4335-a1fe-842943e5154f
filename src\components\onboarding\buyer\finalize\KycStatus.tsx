import {
  Card,
  Card<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Badge,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle } from 'lucide-react';

interface KycStatusProps {
  status: string;
}

const KycStatus: React.FC<KycStatusProps> = ({ status }) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <CheckCircle className="h-4 w-4" />
          KYC Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2">
          <Badge variant={status === 'PASSED' ? 'default' : 'secondary'}>{status}</Badge>
          <p className="text-sm text-gray-600">
            {status === 'PASSED'
              ? 'Your account will be activated immediately'
              : 'Your account will be pending KYC verification'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default KycStatus;
