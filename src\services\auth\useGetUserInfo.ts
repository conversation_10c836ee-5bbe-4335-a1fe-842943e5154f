import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { FinalizeInfoResponse } from '@/types/finalizeResponse.types';
import { useQuery } from '@tanstack/react-query';

export async function getUserInfo(userRole: string): Promise<FinalizeInfoResponse> {
  try {
    const response = await apiClient.get<FinalizeInfoResponse>(`api/v1/users/${userRole}/info/`);
    return response.data!;
  } catch (error) {
    throw handleApiError(error, `Failed to fetch ${userRole} info.`);
  }
}

export const useGetBuyerInfo = (userRole: string) => {
  return useQuery({
    queryKey: [userRole, 'info'],
    queryFn: () => getUserInfo(userRole),
  });
};
