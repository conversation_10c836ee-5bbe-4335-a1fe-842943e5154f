'use client';

import { withBuyerAccessBeforeLogin } from '@/utils/withRoleAccessBeforeLogin';
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kratex-tradetech/kratex-ui-library';
import { ChevronRight, Globe, Loader2, MapPin, Search, X } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useJurisdiction } from './useJurisdiction';

const Jurisdiction = () => {
  const {
    countries,
    states,
    selectedCountry,
    selectedState,
    loadingCountries,
    loadingStates,
    isGeoLookupLoading,
    setSelectedCountry,
    setSelectedState,
    getStateLabel,
    validateForm,
    onJurisdictionSubmit,
  } = useJurisdiction();

  // Search states
  const [countrySearch, setCountrySearch] = useState('');
  const [stateSearch, setStateSearch] = useState('');
  const [isCountrySelectOpen, setIsCountrySelectOpen] = useState(false);
  const [isStateSelectOpen, setIsStateSelectOpen] = useState(false);

  // Filtered countries based on search
  const filteredCountries = useMemo(() => {
    if (!countrySearch.trim()) return countries;

    const searchTerm = countrySearch.toLowerCase().trim();
    return countries.filter(
      (country) =>
        country.name.toLowerCase().includes(searchTerm) || country.emoji?.includes(searchTerm),
    );
  }, [countries, countrySearch]);

  // Filtered states based on search
  const filteredStates = useMemo(() => {
    if (!stateSearch.trim()) return states;

    const searchTerm = stateSearch.toLowerCase().trim();
    return states.filter((state) => state.name.toLowerCase().includes(searchTerm));
  }, [states, stateSearch]);

  const handleCountrySelect = (countryId: string) => {
    const id = Number(countryId);
    const country = countries.find((c) => Number(c.id) === id) || null;
    setSelectedCountry(country);
    setCountrySearch('');
    setIsCountrySelectOpen(false);
  };

  const handleStateSelect = (stateId: string) => {
    const id = Number(stateId);
    const state = states.find((s) => Number(s.id) === id) || null;
    setSelectedState(state);
    setStateSearch('');
    setIsStateSelectOpen(false);
  };

  const clearCountrySearch = () => {
    setCountrySearch('');
  };

  const clearStateSearch = () => {
    setStateSearch('');
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-lg space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 rounded-full">
              <Globe className="h-8 w-8" />
            </div>
          </div>
          <h1 className="text-3xl font-bold">Jurisdiction Information</h1>
          <p className="text-muted-foreground">
            Please select your country and state/province to continue
          </p>
        </div>

        {/* Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Location Details
            </CardTitle>
            <CardDescription>
              This information helps us provide region-specific services and compliance
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Country */}
            <div className="space-y-2">
              <Label htmlFor="country">Country</Label>
              <Select
                value={selectedCountry?.id.toString() || ''}
                onValueChange={handleCountrySelect}
                disabled={loadingCountries}
                open={isCountrySelectOpen}
                onOpenChange={setIsCountrySelectOpen}
              >
                <SelectTrigger className="w-full">
                  {loadingCountries ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Loading countries...
                    </div>
                  ) : isGeoLookupLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Detecting your location...
                    </div>
                  ) : (
                    <SelectValue placeholder="Select a country" />
                  )}
                </SelectTrigger>
                <SelectContent>
                  {/* Search Input */}
                  <div className="flex items-center px-3 pb-2 border-b">
                    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                    <div className="relative flex-1">
                      <input
                        type="text"
                        placeholder="Search countries..."
                        value={countrySearch}
                        onChange={(e) => setCountrySearch(e.target.value)}
                        className="h-8 w-full border-0 bg-transparent p-0 text-sm outline-none focus:ring-0 placeholder:text-muted-foreground"
                        onClick={(e) => e.stopPropagation()}
                        onKeyDown={(e) => e.stopPropagation()}
                        autoComplete="off"
                      />
                      {countrySearch && (
                        <button
                          type="button"
                          className="absolute right-0 top-0 h-8 w-8 p-0 flex items-center justify-center hover:bg-muted rounded-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            clearCountrySearch();
                          }}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  </div>

                  {/* Countries List */}
                  <div className="max-h-48 overflow-y-auto">
                    {filteredCountries.length > 0 ? (
                      filteredCountries.map((country) => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                          <span className="mr-2">{country.emoji}</span>
                          {country.name}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="py-2 px-3 text-sm text-muted-foreground">
                        No countries found
                      </div>
                    )}
                  </div>
                </SelectContent>
              </Select>
            </div>

            {/* State/Province */}
            <div className="space-y-2">
              <Label htmlFor="state">
                {selectedCountry ? getStateLabel(selectedCountry.name) : 'State/Province'}
              </Label>
              <Select
                value={selectedState?.id.toString() ?? ''}
                onValueChange={handleStateSelect}
                disabled={!selectedCountry || loadingStates}
                open={isStateSelectOpen}
                onOpenChange={setIsStateSelectOpen}
              >
                <SelectTrigger className="w-full">
                  <SelectValue
                    placeholder={
                      !selectedCountry
                        ? 'Please select a country first'
                        : loadingStates
                          ? 'Loading...'
                          : `Select ${getStateLabel(selectedCountry.name).toLowerCase()}`
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {/* Search Input */}
                  {selectedCountry && !loadingStates && (
                    <div className="flex items-center px-3 pb-2 border-b">
                      <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                      <div className="relative flex-1">
                        <input
                          type="text"
                          placeholder={`Search ${getStateLabel(selectedCountry.name).toLowerCase()}...`}
                          value={stateSearch}
                          onChange={(e) => setStateSearch(e.target.value)}
                          className="h-8 w-full border-0 bg-transparent p-0 text-sm outline-none focus:ring-0 placeholder:text-muted-foreground"
                          onClick={(e) => e.stopPropagation()}
                          onKeyDown={(e) => e.stopPropagation()}
                          autoComplete="off"
                        />
                        {stateSearch && (
                          <button
                            type="button"
                            className="absolute right-0 top-0 h-8 w-8 p-0 flex items-center justify-center hover:bg-muted rounded-sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              clearStateSearch();
                            }}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* States List */}
                  <div className="max-h-48 overflow-y-auto">
                    {filteredStates.length > 0 ? (
                      filteredStates.map((state) => (
                        <SelectItem key={state.id} value={state.id.toString()}>
                          {state.name}
                        </SelectItem>
                      ))
                    ) : selectedCountry && !loadingStates ? (
                      <div className="py-2 px-3 text-sm text-muted-foreground">
                        No {getStateLabel(selectedCountry.name).toLowerCase()} found
                      </div>
                    ) : null}
                  </div>
                </SelectContent>
              </Select>
            </div>

            {/* Selected Values */}
            {(selectedCountry || selectedState) && (
              <div className="p-4 rounded-lg border">
                <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Selected Jurisdiction
                </h3>
                <div className="space-y-2">
                  {selectedCountry && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Country:</span>
                      <Badge variant="secondary">
                        <span className="mr-1">{selectedCountry.emoji}</span>
                        {selectedCountry.name}
                      </Badge>
                    </div>
                  )}
                  {selectedState && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">
                        {getStateLabel(selectedCountry!.name)}:
                      </span>
                      <Badge variant="secondary">{selectedState.name}</Badge>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Submit */}
        <div className="flex justify-end">
          <Button
            onClick={onJurisdictionSubmit}
            disabled={!validateForm()}
            className="px-8 py-3 font-medium rounded-lg transition-all flex items-center space-x-2"
          >
            <span>Continue</span>
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default withBuyerAccessBeforeLogin(Jurisdiction);
