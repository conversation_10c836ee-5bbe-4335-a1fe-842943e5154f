import { setTokenInCookie } from '@/lib/cookies';
import { useExchangeToken } from '@/services/auth/useExchangeToken';
import { useCountries } from '@/services/geolocation/useGetJurisdiction';
import { useRequestOTP } from '@/services/auth/useRequestOTP';
import { useVerifyOTP } from '@/services/auth/useVerifyOTP';
import { newBuyerStore } from '@/store/newBuyer.store';
import { userStore } from '@/store/user.store';
import { Country } from '@/types/newBuyer.types';
import { UserRole } from '@/types/user.types';
import { useRouter } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'sonner';

const ROLES = [
  { id: 2, name: 'Buyer' },
  { id: 3, name: 'Supplier' },
];

export const useLoginSignup = () => {
  const router = useRouter();
  const { setPhoneNo, setRole } = newBuyerStore();
  const { user, setUser } = userStore();

  // State
  const [phoneNumber, setPhoneNumber] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [role, setRoles] = useState<number>(ROLES[0].id);
  const [loading, setLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState('');
  const [flowId, setFlowId] = useState<string>('');
  const OTP_LENGTH = 6;

  // OTP API hooks
  const requestOTP = useRequestOTP();
  const verifyOTP = useVerifyOTP();
  const exchangeJWT = useExchangeToken();
  const {
    data: countries = [],
    isLoading: loadingCountries,
    error: countriesError,
  } = useCountries();

  useEffect(() => {
    if (user.isRegistered) {
      const redirectPath = '/buyer/dashboard';
      router.push(redirectPath);
    }
  }, [user, router]);

  // Once countries are fetched, set a default countryCode if not set
  useEffect(() => {
    if (!countryCode && !loadingCountries && countries.length > 0) {
      // Prefer India if present, else first in list
      const defaultCountry = countries.find((c) => c.iso2?.toUpperCase() === 'IN') || countries[0];
      if (defaultCountry && defaultCountry.phone_code) {
        setCountryCode(defaultCountry.phone_code);
      } else if (defaultCountry) {
        // If phone_code missing, you might set empty or skip
        setCountryCode(''); // or some fallback
      }
    }
  }, [countries, loadingCountries, countryCode]);

  // Derive selectedCountry object from countryCode
  const selectedCountry: Country | null = useMemo(() => {
    if (!countryCode) return null;
    return countries.find((c) => c.phone_code === countryCode) || null;
  }, [countries, countryCode]);

  const isPhoneNumberValid = useMemo(() => {
    return phoneNumber.trim().length > 0 && selectedCountry !== null;
  }, [phoneNumber, selectedCountry]);

  // Handlers
  const handlePhoneSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    if (!isPhoneNumberValid) {
      toast.error('Enter a valid phone number');
      return;
    }

    setLoading(true);
    try {
      const fullNumber = `+${countryCode}${phoneNumber}`;
      const { flow_id, message } = await requestOTP.mutateAsync({ phone_number: fullNumber });
      setFlowId(flow_id);
      setOtpSent(true);
      toast.success(`${message}`);
    } catch (error) {
      toast.error(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message || 'Failed to send OTP'
          : 'Failed to send OTP',
      );
    } finally {
      setLoading(false);
    }
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (otp.length !== OTP_LENGTH || !flowId) return;

    setLoading(true);
    try {
      const fullNumber = `+${countryCode}${phoneNumber}`;
      const { code, state } = await verifyOTP.mutateAsync({
        phone_number: fullNumber,
        otp,
        flow_id: flowId,
      });

      const { id_token, is_registered } = await exchangeJWT.mutateAsync({
        code,
        state,
      });

      setTokenInCookie(id_token);
      setUser({ ...user, isRegistered: is_registered });

      if (is_registered) {
        const dashboard = role === UserRole.BUYER ? '/buyer/dashboard' : '/supplier/dashboard';
        router.push(dashboard);
      } else {
        setPhoneNo(`+${countryCode}${phoneNumber}`);
        setRole(role);
        const next = role === UserRole.BUYER ? '/buyer/jurisdiction' : '/supplier/jurisdiction';
        router.push(next);
      }
    } catch (error) {
      toast.error(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message || 'OTP verification failed'
          : 'OTP verification failed',
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResendOTP = async () => {
    if (!isPhoneNumberValid) {
      toast.error('Enter a valid phone number');
      return;
    }

    setLoading(true);
    try {
      const fullNumber = `+${countryCode}${phoneNumber}`;
      const { flow_id, message } = await requestOTP.mutateAsync({ phone_number: fullNumber });
      setFlowId(flow_id);
      setOtpSent(true);
      toast.success(`${message}`);
    } catch (error) {
      toast.error(
        typeof error === 'object' && error !== null && 'message' in error
          ? (error as { message?: string }).message || 'Failed to resend OTP'
          : 'Failed to resend OTP',
      );
    } finally {
      setLoading(false);
    }
  };

  const handleChangePhone = () => {
    setOtpSent(false);
    setOtp('');
  };

  return {
    // State
    phoneNumber,
    setPhoneNumber,
    countryCode,
    setCountryCode,
    role,
    setRoles,
    loading,
    otpSent,
    otp,
    setOtp,
    countries,
    user,

    // Computed values
    countriesError,
    loadingCountries,
    selectedCountry,
    isPhoneNumberValid,

    // Handlers
    handlePhoneSubmit,
    handleOtpSubmit,
    handleChangePhone,
    handleResendOTP,

    // Constants
    ROLES,
  };
};
