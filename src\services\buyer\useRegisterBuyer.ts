import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { useMutation } from '@tanstack/react-query';

export interface BuyerRegistrationPayload {
  organisation_type: number;
  jurisdiction: number;
  buyer_details?: Record<string, any>;
}

export interface BuyerRegistrationResponse {
  id: number;
  phone: string;
  email: string | null;
  is_active: boolean;
  user_type: number;
}

export async function registerBuyer(
  payload: BuyerRegistrationPayload,
): Promise<BuyerRegistrationResponse> {
  try {
    const response = await apiClient.post<BuyerRegistrationResponse>(
      '/api/v1/users/buyer/registration/',
      payload,
    );
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Buyer registration failed.');
  }
}

export const useBuyerRegister = () => {
  return useMutation<BuyerRegistrationResponse, Error, BuyerRegistrationPayload>({
    mutationFn: (payload) => registerBuyer(payload),
  });
};
