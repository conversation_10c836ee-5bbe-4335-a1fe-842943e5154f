import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Button,
} from '@kratex-tradetech/kratex-ui-library';
import { Edit, Building } from 'lucide-react';

interface OrganizationInfoData {
  org_type: string;
  org_name: string;
  registration_number: string;
  tax_id: string;
  business_address: string;
}

interface OrganizationInfoProps {
  data: OrganizationInfoData;
  onEdit: (section: string) => void;
}

const OrganizationInfo: React.FC<OrganizationInfoProps> = ({ data, onEdit }) => {
  const orgTypeLabels: Record<string, string> = {
    REGISTERED_COMPANY: 'Registered Company',
    PARTNERSHIP: 'Partnership',
    SOLE_PROPRIETORSHIP: 'Sole Proprietorship',
    LLC: 'Limited Liability Company',
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Building className="h-4 w-4" />
            Organization Information
          </CardTitle>
          <Button variant="outline" size="sm" onClick={() => onEdit('organization')}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-600">Organization Type</p>
            <p className="text-sm">{orgTypeLabels[data.org_type]}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Organization Name</p>
            <p className="text-sm">{data.org_name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Registration Number</p>
            <p className="text-sm">{data.registration_number}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Tax ID</p>
            <p className="text-sm">{data.tax_id}</p>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600">Business Address</p>
          <p className="text-sm">{data.business_address}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrganizationInfo;
