'use client';

import { useEffect, useMemo } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { newBuyerStore } from '../store/newBuyer.store';
import { userStore } from '@/store/user.store';

export enum UserRole {
  ADMIN = 1,
  BUYER = 2,
  SUPPLIER = 3,
}

// Loading Components
const DashboardRedirectLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-2" />
      <p>Redirecting to Dashboard</p>
    </div>
  </div>
);

const AccessDeniedLoader = ({ role }: { role: 'buyer' | 'supplier' }) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
      <p className="text-muted-foreground mb-4">You don&#39;t have access to {role} flow</p>
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-black mx-auto mb-2" />
    </div>
  </div>
);

// Main HOC
export function withRoleAccessBeforeLogin<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  requiredRole: UserRole.BUYER | UserRole.SUPPLIER,
) {
  return function WithRoleAccessBeforeLoginComponent(props: P) {
    const router = useRouter();
    const pathname = usePathname();
    const { newBuyerData } = newBuyerStore();
    const { user } = userStore();

    const roleConfig = useMemo(() => {
      return {
        [UserRole.BUYER]: {
          pathPrefix: '/buyer',
          dashboardPath: '/buyer/dashboard',
          roleName: 'buyer' as const,
        },
        [UserRole.SUPPLIER]: {
          pathPrefix: '/supplier',
          dashboardPath: '/supplier/dashboard',
          roleName: 'supplier' as const,
        },
      };
    }, []);

    const currentRoleConfig = roleConfig[requiredRole];

    const hasAccess = useMemo(() => {
      return pathname.includes(currentRoleConfig.pathPrefix) && newBuyerData.role === requiredRole;
    }, [pathname, newBuyerData.role, requiredRole, currentRoleConfig.pathPrefix]);

    // Redirect unauthorized users to login-signup
    useEffect(() => {
      if (!hasAccess) {
        const timeoutId = setTimeout(() => {
          router.push('/login-signup');
        }, 3000);

        return () => clearTimeout(timeoutId);
      }
    }, [hasAccess, router]);

    // Redirect authenticated users to dashboard
    useEffect(() => {
      if (user?.isRegistered) {
        const timeoutId = setTimeout(() => {
          router.push(currentRoleConfig.dashboardPath);
        }, 2000);

        return () => clearTimeout(timeoutId);
      }
    }, [user, router, currentRoleConfig.dashboardPath]);

    // Show loading when user is authenticated (redirecting to dashboard)
    if (user?.isRegistered) {
      return <DashboardRedirectLoader />;
    }

    // Show access denied when user doesn't have access (redirecting to login-signup)
    if (!hasAccess) {
      return <AccessDeniedLoader role={currentRoleConfig.roleName} />;
    }

    // Render the wrapped component if user has access and is not authenticated
    return <WrappedComponent {...props} />;
  };
}

// Convenience HOCs for specific roles
export function withBuyerAccessBeforeLogin<P extends object>(
  WrappedComponent: React.ComponentType<P>,
) {
  return withRoleAccessBeforeLogin(WrappedComponent, UserRole.BUYER);
}

export function withSupplierAccessBeforeLogin<P extends object>(
  WrappedComponent: React.ComponentType<P>,
) {
  return withRoleAccessBeforeLogin(WrappedComponent, UserRole.SUPPLIER);
}
