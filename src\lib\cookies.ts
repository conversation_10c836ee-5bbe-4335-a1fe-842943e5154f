import Cookies from 'js-cookie';

export enum CookieName {
  token = 'token',
}

const COOKIE_EXPIRY = 7; // 7 days

const TOKEN_NAME = CookieName.token;

export const setCookie = (cookieName: CookieName, value: unknown) => {
  Cookies.set(cookieName, value as string, { expires: COOKIE_EXPIRY });
};

export const getCookie = (cookieName: CookieName) => {
  return Cookies.get(cookieName) || '';
};

export const clearCookie = (cookieName: CookieName) => {
  Cookies.remove(cookieName);
};

// for token only
export const setTokenInCookie = (value: unknown) => {
  setCookie(TOKEN_NAME, value);
};

export const getTokenFromCookie = () => {
  return getCookie(TOKEN_NAME);
};

export const clearTokenFromCookie = () => {
  clearCookie(TOKEN_NAME);
};
