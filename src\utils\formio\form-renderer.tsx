import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@kratex-tradetech/kratex-ui-library';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { Card, CardContent } from '@kratex-tradetech/kratex-ui-library';
import type { FormioSchema, FormioComponent, FormioColumnsComponent } from './types';
import { createZodSchema, getDefaultValues } from './schema-generator';
import { FormioComponentRenderer } from './componentRenderer';
import { ColumnRenderer } from './column-renderer';
import { newBuyerStore } from '@/store/newBuyer.store';

interface FormioFormRendererProps {
  formioJson: FormioSchema;
  onSubmit?: (data: any) => void;
  className?: string;
  showCard?: boolean;
}

export const FormioFormRenderer: React.FC<FormioFormRendererProps> = ({
  formioJson,
  onSubmit,
  className = '',
  showCard = true,
}) => {
  const { newBuyerData } = newBuyerStore();
  const schema = createZodSchema(formioJson.components);

  const form = useForm({
    resolver: zodResolver(schema),
    defaultValues: getDefaultValues(formioJson.components),
  });

  // Function to recursively check if email component exists in the form
  const hasEmailComponent = useMemo(() => {
    const checkForEmailComponent = (
      components: (FormioComponent | FormioColumnsComponent)[],
    ): boolean => {
      return components.some((component) => {
        if (component.type === 'email') {
          return true;
        }
        if (component.type === 'columns') {
          const columnsComponent = component as FormioColumnsComponent;
          return columnsComponent.columns.some((column) =>
            checkForEmailComponent(column.components || []),
          );
        }
        return false;
      });
    };

    return checkForEmailComponent(formioJson.components);
  }, [formioJson.components]);

  // Check if email is verified (you can adjust this logic based on your email verification implementation)
  const isEmailVerified = useMemo(() => {
    if (!hasEmailComponent) return true; // If no email component, don't block submission

    // Check if email exists and is verified in the store
    // You might want to add an `emailVerified` field to your store
    const isEmailVerified = newBuyerData?.isEmailVerified;

    // For now, we'll check if email exists and is not empty
    // You should replace this with your actual email verification check
    return isEmailVerified;

    // If you have an emailVerified field in your store, use this instead:
    // return newBuyerData?.accountBasics?.emailVerified === true;
  }, [hasEmailComponent, newBuyerData.isEmailVerified]);

  const handleSubmit = (data: Record<string, unknown>, formJson: FormioSchema) => {
    if (hasEmailComponent && !isEmailVerified) {
      form.setError('email', {
        type: 'manual',
        message: 'Please verify your email address before submitting the form.',
      });
      return;
    }
    if (onSubmit) {
      onSubmit(data);
    }
  };

  const renderComponents = (components: (FormioComponent | FormioColumnsComponent)[]) => {
    return components.map((component, index) => {
      if (component.type === 'columns') {
        const columnsComponent = component as FormioColumnsComponent;
        return (
          <div key={index} className="flex flex-wrap -mx-2 mb-6">
            {columnsComponent.columns.map((column, colIndex) => (
              <ColumnRenderer key={colIndex} column={column} form={form} />
            ))}
          </div>
        );
      } else {
        const singleComponent = component as FormioComponent;
        return (
          <div key={singleComponent.key || index} className="mb-4">
            <FormioComponentRenderer component={singleComponent} form={form} />
          </div>
        );
      }
    });
  };

  const FormContent = () => (
    <>
      <h2 className="text-2xl font-bold mb-6">{formioJson.title}</h2>
      <Form {...form}>
        <div className="space-y-6">
          {renderComponents(formioJson.components)}
          <Button
            type="button"
            onClick={form.handleSubmit(() => {
              handleSubmit(form.getValues(), formioJson);
            })}
            className="w-full"
            disabled={hasEmailComponent && !isEmailVerified}
          >
            Submit
          </Button>

          {/* Optional: Show email verification status */}
          {hasEmailComponent && !isEmailVerified && (
            <div className="text-center text-sm text-muted-foreground">
              Please enter your details and verify your email address to enable form submission
            </div>
          )}
        </div>
      </Form>
    </>
  );

  if (showCard) {
    return (
      <Card className={`w-full max-w-4xl mx-auto ${className}`}>
        <CardContent className="p-6">
          <FormContent />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <FormContent />
    </div>
  );
};
