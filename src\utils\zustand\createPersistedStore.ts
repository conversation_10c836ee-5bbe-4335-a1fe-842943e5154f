/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from 'zustand';
import { persist, PersistOptions } from 'zustand/middleware';

// Helper type to infer store creators
type StoreCreator<T> = (set: any, get: any, api: any) => T;

/**
 * createPersistedStore: Wraps zustand create with persist middleware.
 * @param name - The key under which to store data in localStorage (or other storage).
 * @param config - The store configuration function (set, get, api) => state slice.
 * @param options - Optional PersistOptions to further configure (versioning, migrate, partialize, etc.).
 * @returns A zustand store with persistence enabled.
 */
export function createPersistedStore<T extends object>(
  name: string,
  config: StoreCreator<T>,
  options?: Omit<PersistOptions<T>, 'name'>,
) {
  return create<T>()(
    persist((set, get, api) => config(set, get, api), {
      name,
      ...options,
    }),
  );
}
