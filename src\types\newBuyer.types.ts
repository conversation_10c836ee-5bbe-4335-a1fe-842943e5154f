/* eslint-disable @typescript-eslint/no-unused-vars */

import { UserRole } from './user.types';

/* eslint-disable @typescript-eslint/no-explicit-any */
export type PhoneNo = string;
export type Country = {
  id: number;
  name: string;
  emoji: string;
  currency: string;
  phone_code: string;
  iso3: string;
  iso2: string;
};
export type State = { id: number; name: string; state_code: string; iso2: string };
export type Jurisdiction = {
  country: Country;
  state: State;
};
export type OrgType = { id: number; name: string };
export type AccountBasics = {
  [key: string]: any;
};

export type NewBuyerData = {
  phoneNo: PhoneNo;
  country: Country;
  state: State;
  jurisdiction: Jurisdiction;
  orgType: OrgType;
  accountBasics: AccountBasics;
  isEmailVerified?: boolean;
  role: UserRole;
};
