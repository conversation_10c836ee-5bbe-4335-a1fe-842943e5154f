'use client';

import { Card, CardContent } from '@kratex-tradetech/kratex-ui-library';
import { FileText, Loader2 } from 'lucide-react';
import { FormioFormRenderer } from '@/utils/formio';
import { useAccountBasics } from './useAccountBasics';
import { withBuyerAccessBeforeLogin } from '@/utils/withRoleAccessBeforeLogin';

const DynamicAccountBasics = () => {
  const { isSubmitting, schemaLoading, formSchema, handleFormSubmit } = useAccountBasics();

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="w-full max-w-3xl space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 rounded-full flex items-center justify-center border">
              <FileText className="w-8 h-8" />
            </div>
          </div>
          <h1 className="text-3xl font-bold">Account Basics</h1>
          <p className="text-lg">Complete your account setup with required information</p>
        </div>
        <Card className="border shadow">
          {schemaLoading ? (
            <div className="flex items-center justify-center gap-2 p-6">
              <Loader2 className="h-4 w-4 animate-spin" />
              Loading your form
            </div>
          ) : (
            <CardContent className="p-6">
              {formSchema && (
                <FormioFormRenderer
                  formioJson={formSchema}
                  onSubmit={handleFormSubmit}
                  showCard={false}
                />
              )}

              {isSubmitting && (
                <div className="mt-6 flex items-center justify-center space-x-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Creating Account...</span>
                </div>
              )}
            </CardContent>
          )}
        </Card>
      </div>
    </div>
  );
};

export default withBuyerAccessBeforeLogin(DynamicAccountBasics);
