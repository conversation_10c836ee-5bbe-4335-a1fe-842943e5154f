// Centralized query key management
export const queryKeys = {
  // Auth
  auth: {
    all: ['auth'] as const,
    me: () => [...queryKeys.auth.all, 'me'] as const,
  },

  // Jurisdiction
  jurisdiction: {
    all: ['jurisdiction'] as const,
    countries: () => [...queryKeys.jurisdiction.all, 'countries'] as const,
    states: () => [...queryKeys.jurisdiction.all, 'states'] as const,
    statesByCountry: (countryId: number) =>
      [...queryKeys.jurisdiction.states(), countryId] as const,
  },

  // Organisation Types
  orgType: {
    all: ['orgType'] as const,
    list: () => [...queryKeys.orgType.all, 'list'] as const,
    details: (id: string) => [...queryKeys.orgType.all, 'details', id] as const,
  },

  // Buyer
  buyer: {
    all: ['buyer'] as const,
    dashboard: () => [...queryKeys.buyer.all, 'dashboard'] as const,
    sourcing: () => [...queryKeys.buyer.all, 'sourcing'] as const,
    suppliers: () => [...queryKeys.buyer.all, 'suppliers'] as const,
  },

  // Supplier
  supplier: {
    all: ['supplier'] as const,
    dashboard: () => [...queryKeys.supplier.all, 'dashboard'] as const,
    catalog: () => [...queryKeys.supplier.all, 'catalog'] as const,
    orders: () => [...queryKeys.supplier.all, 'orders'] as const,
  },

  // Account Basics (Form Definitions)
  accountBasics: {
    all: ['accountBasics'] as const,
    formDefinition: (keyword: string) =>
      [...queryKeys.accountBasics.all, 'formDefinition', keyword] as const,
  },
} as const;
