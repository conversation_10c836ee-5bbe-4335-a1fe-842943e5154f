'use client';

import { ChevronDown, X, Check, Search } from 'lucide-react';
import { useMultiSelectDropdown } from './useMultiSelectDropdown';
import { DropdownOption } from './types';

interface MultiSelectDropdownProps {
  placeholder: string;
  options: DropdownOption[];
  selectedValues: DropdownOption[];
  onSelectionChange: (value: DropdownOption) => void;
  id?: string;
  isDataLoading?: boolean;
}

const MultiSelectDropdown = ({
  placeholder,
  options,
  selectedValues,
  onSelectionChange,
  id,
  isDataLoading = false,
}: MultiSelectDropdownProps) => {
  const {
    isOpen,
    searchTerm,
    filteredOptions,
    showSearch,
    dropdownRef,
    searchInputRef,
    handleOptionClick,
    handleRemoveTag,
    handleSearchChange,
    handleDropdownToggle,
  } = useMultiSelectDropdown(options, selectedValues, onSelectionChange);

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Trigger */}
      <div
        id={id}
        className="flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background cursor-pointer focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
        onClick={handleDropdownToggle}
      >
        <div className="flex-1">
          {isDataLoading ? (
            <span className="text-muted-foreground">...data loading</span>
          ) : selectedValues.length === 0 ? (
            <span className="text-muted-foreground">{placeholder}</span>
          ) : (
            <div className="flex flex-wrap gap-1">
              {selectedValues.map((value) => (
                <span
                  key={value.id}
                  className="inline-flex items-center gap-1 rounded bg-secondary px-2 py-1 text-xs"
                >
                  {value.name}
                  <button
                    type="button"
                    onClick={(e) => handleRemoveTag(value, e)}
                    className="hover:bg-secondary-foreground/20 rounded-full p-0.5"
                  >
                    <X size={12} />
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
        <ChevronDown size={16} className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown */}
      {isOpen && !isDataLoading && (
        <div className="absolute z-50 w-full mt-1 bg-popover border border-border rounded-md shadow-md max-h-60 overflow-hidden">
          {/* Search Input */}
          {showSearch && (
            <div className="p-2 border-b border-border">
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-2 top-1/2 transform -translate-y-1/2 text-muted-foreground"
                />
                <input
                  ref={searchInputRef}
                  type="text"
                  placeholder="Search options..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full pl-8 pr-3 py-2 text-sm bg-background border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
            </div>
          )}

          {/* Options List */}
          <div className="max-h-48 overflow-auto">
            {filteredOptions.length === 0 ? (
              <div className="px-3 py-2 text-sm text-muted-foreground">No options found</div>
            ) : (
              filteredOptions.map((option) => {
                const isSelected = selectedValues.some((selected) => selected.id === option.id);
                return (
                  <div
                    key={option.id}
                    className="flex items-center justify-between px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground"
                    onClick={() => handleOptionClick(option)}
                  >
                    <span>{option.name}</span>
                    {isSelected && <Check size={16} className="text-primary" />}
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;
