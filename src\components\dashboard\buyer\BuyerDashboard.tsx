'use client';

import { Avatar, AvatarFallback, Button } from '@kratex-tradetech/kratex-ui-library';
import { Bell, LogOut } from 'lucide-react';
import { useBuyerDashboard } from './useBuyerDashboard';
import { withBuyerRoute } from '@/utils/withRoleAccess';

const BuyerDashboard: React.FC = () => {
  const { getUserInitials, user, isPopupOpen, popupRef, handleAvatarClick, handleLogout } =
    useBuyerDashboard();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Buyer Dashboard</h1>
            <p className="text-muted-foreground mt-1">
              Welcome back, {`${user?.GivenName} ${user?.Surname}`}! Here&apos;s what&apos;s
              happening with your orders.
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" aria-label="Notifications">
              <Bell className="h-4 w-4" />
            </Button>

            {/* Avatar with Popup */}
            <div className="relative" ref={popupRef}>
              <Avatar
                className="cursor-pointer hover:ring-2 hover:ring-primary hover:ring-offset-2 transition-all"
                onClick={handleAvatarClick}
              >
                <AvatarFallback>
                  {getUserInitials(`${user?.GivenName} ${user?.Surname}` || 'Buyer')}
                </AvatarFallback>
              </Avatar>

              {/* Popup Menu */}
              {isPopupOpen && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                  <div className="py-1">
                    {/* User Info */}
                    <div className="px-4 py-2 border-b border-gray-100">
                      <p className="text-sm font-medium text-gray-900">
                        {`${user?.GivenName} ${user?.Surname}`}
                      </p>
                      <p className="text-xs text-gray-500">{user?.Email}</p>
                    </div>

                    {/* Logout Button */}
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 flex items-center gap-2 transition-colors"
                    >
                      <LogOut className="h-4 w-4" />
                      Logout
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="space-y-6">
          {/* Add your dashboard content here */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Placeholder cards - replace with actual dashboard content */}
            <div className="p-6 bg-card rounded-lg border">
              <h3 className="font-semibold mb-2">Active Orders</h3>
              <p className="text-muted-foreground">Your current orders</p>
            </div>

            <div className="p-6 bg-card rounded-lg border">
              <h3 className="font-semibold mb-2">Recent Activity</h3>
              <p className="text-muted-foreground">Latest updates</p>
            </div>

            <div className="p-6 bg-card rounded-lg border">
              <h3 className="font-semibold mb-2">Quick Actions</h3>
              <p className="text-muted-foreground">Common tasks</p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default withBuyerRoute(BuyerDashboard);
