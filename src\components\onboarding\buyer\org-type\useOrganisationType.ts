'use client';

import { useGetOrganisationTypes } from '@/services/auth/useGetOrganisationType';
import { newBuyerStore, useNewBuyerJurisdiction, useNewBuyerRole } from '@/store/newBuyer.store';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { OrganisationTypeOption } from './type';

export const useOrganisationType = () => {
  const router = useRouter();
  const buyerJurisdiction = useNewBuyerJurisdiction();
  const role = useNewBuyerRole();
  const [selectedType, setSelectedType] = useState<string>('');
  const { setOrgType } = newBuyerStore();

  // Fetch org types via API
  const {
    data: organisationTypes = [],
    isLoading,
    error,
  } = useGetOrganisationTypes(buyerJurisdiction.country?.id, String(role));

  const proceed = () => {
    if (selectedType) {
      const selectedOrg = organisationTypes.find(
        (type: OrganisationTypeOption) => type.name === selectedType,
      );
      if (selectedOrg) {
        setOrgType({ id: selectedOrg.id, name: selectedOrg.name });
      }
      router.push('/buyer/account-basics');
    }
  };

  return {
    isLoading,
    selectedType,
    setSelectedType,
    organisationTypes,
    proceed,
    error,
  };
};
