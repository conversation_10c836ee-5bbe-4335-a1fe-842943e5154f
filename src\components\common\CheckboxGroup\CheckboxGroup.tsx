import { Checkbox, Label } from '@kratex-tradetech/kratex-ui-library';

interface CheckboxGroupProps {
  label: string;
  options: string[];
  selectedValues: string[];
  onSelectionChange: (value: string) => void;
  gridCols?: 'grid-cols-2' | 'grid-cols-3' | 'grid-cols-4';
  idPrefix: string;
}

const CheckboxGroup = ({
  label,
  options,
  selectedValues,
  onSelectionChange,
  gridCols = 'grid-cols-2',
  idPrefix,
}: CheckboxGroupProps) => {
  return (
    <div className="space-y-2">
      <Label className="text-base font-medium">{label}</Label>
      <div className={`grid ${gridCols} gap-2`}>
        {options.map((option) => (
          <div key={option} className="flex items-center space-x-2">
            <Checkbox
              id={`${idPrefix}-${option}`}
              checked={selectedValues.includes(option)}
              onCheckedChange={() => onSelectionChange(option)}
            />
            <Label htmlFor={`${idPrefix}-${option}`} className="text-sm font-normal cursor-pointer">
              {option}
            </Label>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CheckboxGroup;
