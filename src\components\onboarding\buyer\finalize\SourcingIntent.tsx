'use client';

import {
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Button,
  Badge,
} from '@kratex-tradetech/kratex-ui-library';
import { Edit, Globe } from 'lucide-react';

interface SourcingIntentData {
  sourcing_for: string[];
  sourcing_from: string[];
  use_case: string;
  use_case_enum?: number;
  pref_shipping_term: string;
  pref_shipping_term_enum?: number;
  target_lead_time: string;
  target_lead_time_enum?: number;
  product_categories: string[];
  compliance_reqs: string[];
  compliance_reqs_enum?: number[];
}

interface SourcingIntentProps {
  data: SourcingIntentData;
  onEdit: (section: string) => void;
}

const SourcingIntent: React.FC<SourcingIntentProps> = ({ data, onEdit }) => {
  const renderArrayField = (items: string[], enums?: number[]) => {
    if (!items || items.length === 0) {
      return <p className="text-sm text-gray-500">None specified</p>;
    }
    return (
      <div className="flex flex-wrap gap-1">
        {items.map((item, index) => (
          <Badge key={index} variant="outline" className="text-xs">
            {item}
            {enums && enums[index] !== undefined ? ` (enum: ${enums[index]})` : ''}
          </Badge>
        ))}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Globe className="h-4 w-4" />
            Sourcing Intent
          </CardTitle>
          <Button variant="outline" size="sm" onClick={() => onEdit('sourcing')}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-600 mb-2">Sourcing For</p>
            {renderArrayField(data.sourcing_for)}
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600 mb-2">Sourcing From</p>
            {renderArrayField(data.sourcing_from)}
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Use Case</p>
            <p className="text-sm">
              {data.use_case}
              {data.use_case_enum !== undefined ? ` (enum: ${data.use_case_enum})` : ''}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Shipping Term</p>
            <p className="text-sm">
              {data.pref_shipping_term}
              {data.pref_shipping_term_enum !== undefined
                ? ` (enum: ${data.pref_shipping_term_enum})`
                : ''}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Lead Time</p>
            <p className="text-sm">
              {data.target_lead_time}
              {data.target_lead_time_enum !== undefined
                ? ` (enum: ${data.target_lead_time_enum})`
                : ''}
            </p>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600 mb-2">Product Categories</p>
          {renderArrayField(data.product_categories)}
        </div>
        <div>
          <p className="text-sm font-medium text-gray-600 mb-2">Compliance Requirements</p>
          {renderArrayField(data.compliance_reqs, data.compliance_reqs_enum)}
        </div>
      </CardContent>
    </Card>
  );
};

export default SourcingIntent;
