import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { PaginatedResponse } from '@/types/api';
import { Country, State } from '@/types/newBuyer.types';
import { queryKeys } from '@/lib/query-keys';
import { useQuery, useQueryClient } from '@tanstack/react-query';

// Hook for fetching countries
export const useCountries = () => {
  return useQuery({
    queryKey: queryKeys.jurisdiction.countries(),
    queryFn: getCountries,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

// Hook for fetching single country
export const useCountry = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.jurisdiction.countries(), id],
    queryFn: () => getCountry(id),
    enabled: !!id,
    staleTime: 30 * 60 * 1000,
  });
};

// Hook for fetching states
export const useStates = (countryId?: number) => {
  return useQuery({
    queryKey: countryId
      ? queryKeys.jurisdiction.statesByCountry(countryId)
      : queryKeys.jurisdiction.states(),
    queryFn: () => getStates(countryId),
    enabled: !!countryId, // Only run when countryId is provided
    staleTime: 30 * 60 * 1000,
  });
};

// Hook for fetching single state
export const useState = (id: string) => {
  return useQuery({
    queryKey: [...queryKeys.jurisdiction.states(), id],
    queryFn: () => getState(id),
    enabled: !!id,
    staleTime: 30 * 60 * 1000,
  });
};

// Hook for prefetching countries
export const usePrefetchCountries = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.jurisdiction.countries(),
      queryFn: getCountries,
      staleTime: 30 * 60 * 1000,
    });
  };
};

// Hook for prefetching states by country
export const usePrefetchStates = () => {
  const queryClient = useQueryClient();

  return (countryId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.jurisdiction.statesByCountry(Number(countryId)),
      queryFn: () => getStates(Number(countryId)),
      staleTime: 30 * 60 * 1000,
    });
  };
};

// ==================== COUNTRIES ====================

// Get all countries (paginated)
export async function getCountries(): Promise<Country[]> {
  try {
    const response = await apiClient.get<PaginatedResponse<Country>>(
      '/api/v1/geolocation/countries/',
    );
    return response.data?.results || [];
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch countries');
  }
}

// Get country by ID
export async function getCountry(id: string): Promise<Country | undefined> {
  try {
    // TODO: refactor all api urls into an object
    const response = await apiClient.get<Country>(`/api/v1/geolocation/countries/${id}`);
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch country');
  }
}

// ==================== STATES ====================

// Get all states or states by country (paginated)
export async function getStates(country?: number): Promise<State[]> {
  try {
    const params = country ? { country } : {};
    const response = await apiClient.get<PaginatedResponse<State>>('/api/v1/geolocation/states/', {
      params,
    });
    return response.data?.results || [];
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch states');
  }
}

// Get state by ID
export async function getState(id: string): Promise<State | undefined> {
  try {
    const response = await apiClient.get<State>(`/api/v1/geolocation/states/${id}`);
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch state');
  }
}
