on:
  push:
    branches:
      - main
  pull_request:
    types: [review_requested]
    branches:
      - main
      - test-instance

permissions:
  id-token: write
  contents: read
  packages: read
  repository-projects: read

jobs:
  Test:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 10
          run_install: false

      - name: Setup NodeJS 20
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: 'https://npm.pkg.github.com/'
          scope: '@kratex-tradetech'
          always-auth: true
          cache: 'pnpm'
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile
        env:
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Run ESLint
        run: pnpm run lint

      - name: Run Prettier & Type check
        run: pnpm run ci:check

      - name: Run Unit Tests
        run: pnpm run test

      - name: Generate Coverage Report
        run: pnpm run test:coverage

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage

  Build:
    name: Build & Push to ECR
    needs: Test
    if: github.event_name != 'pull_request'
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID }}:role/kratex-builder-role
          aws-region: ap-south-1
          role-session-name: oidc_github

      - name: Image Name
        id: image-name
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "name=kratex-dev-frontend" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Environment
        id: environment
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "name=dev" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Set Release Tag
        id: release-tag
        shell: bash
        run: |
          PREVIOUS_TAG=$(aws ssm get-parameter --name  "/kratex/${{ steps.environment.outputs.name }}/FRONTEND_RELEASE_TAG" --region ap-south-1 | jq -r ".Parameter.Value")
          echo "tag=$((++PREVIOUS_TAG))" >> $GITHUB_OUTPUT
          aws ssm put-parameter --name "/kratex/${{ steps.environment.outputs.name }}/FRONTEND_RELEASE_TAG" --type "String" --overwrite --value "$PREVIOUS_TAG"

      - run: aws sts get-caller-identity

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          mask-password: true

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set Secrets
        id: secrets
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "api_url=${{ secrets.NEXT_PUBLIC_API_URL }}" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Build & Push
        uses: docker/build-push-action@v6
        with:
          push: true
          context: .
          platforms: linux/amd64
          secrets: |
            "api_url=${{ steps.secrets.outputs.api_url }}"
            "node_token=${{ secrets.GITHUB_TOKEN }}"
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ steps.image-name.outputs.name }}:${{ steps.release-tag.outputs.tag }}

  Deploy:
    needs: Build
    name: Deploy to Server
    runs-on: ubuntu-24.04

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{ secrets.AWS_ACCOUNT_ID }}:role/kratex-builder-role
          aws-region: ap-south-1
          role-session-name: oidc_github

      - name: Image Name
        id: image-name
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "name=kratex-dev-frontend" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Container Name
        id: container-name
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "name=kratex-app" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Log Group
        id: log-group
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "name=kratex-dev-log-group" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Environment
        id: environment
        shell: bash
        run: |
          case "${{ github.ref }}" in
            "refs/heads/main")
              echo "name=dev" >> $GITHUB_OUTPUT
            ;;
          esac

      - name: Get Release Tag
        id: release-tag
        run: |
          RELEASE_TAG=$(aws ssm get-parameter --name  "/kratex/${{ steps.environment.outputs.name }}/FRONTEND_RELEASE_TAG" --region ap-south-1 | jq -r ".Parameter.Value")
          echo "tag=$RELEASE_TAG" >> $GITHUB_OUTPUT

      - name: Set Secrets
        run: |
          sed -i "s/aws_account_id:.*$/aws_account_id: ${{ secrets.AWS_ACCOUNT_ID }}/" .ci/deploy.yml
          sed -i "s/aws_ecr_repo:.*$/aws_ecr_repo: ${{ steps.image-name.outputs.name }}/" .ci/deploy.yml
          sed -i "s/image_tag:.*$/image_tag: ${{ steps.release-tag.outputs.tag }}/" .ci/deploy.yml
          sed -i "s/container_name:.*$/container_name: ${{ steps.container-name.outputs.name }}/" .ci/deploy.yml

      - name: Push new Playbook
        run: |
          aws s3 cp .ci/deploy.yml s3://kratex-deploy/${{ steps.environment.outputs.name }}.yml

      - name: Trigger Deployment via SSM
        id: ssm-trigger
        run: |
          command_id=$(aws ssm send-command --document-name "AWS-RunAnsiblePlaybook" \
                               --targets "Key=tag:Name,Values=kratex-dev" \
                               --max-errors 1 \
                               --parameters '{"playbookurl":["s3://kratex-deploy/${{ steps.environment.outputs.name }}.yml"]}' \
                               --timeout-seconds 600 \
                               --region ap-south-1 \
                               --output text \
                               --query "Command.CommandId")
          echo "id=$command_id" >> $GITHUB_OUTPUT

      - name: Check Deployment Status
        run: |
          command_id=${{ steps.ssm-trigger.outputs.id }}
          status="Pending"
          while [ "$status" = "Pending" -o "$status" = "InProgress" ]; do
            echo "Waiting for command to complete... (polling every 10 seconds)"
            sleep 10
            status=$(aws ssm list-command-invocations --command-id $command_id \
                      --details --output text --query "CommandInvocations[*].Status")
            echo "Current status: $status"
          done
          if [ "$status" = "Success" ]; then
            echo "Command executed successfully."
          else
            echo "Command failed with status: $status. Logs:"
            instance_id=$(aws ssm list-command-invocations --command-id $command_id \
              --details --output text --query "CommandInvocations[*].InstanceId")
            aws ssm get-command-invocation \
              --command-id $command_id \
              --instance-id $instance_id \
              --output text \
              --query '{StandardOutputContent:StandardOutputContent,StandardErrorContent:StandardErrorContent}'
            exit 1
          fi
