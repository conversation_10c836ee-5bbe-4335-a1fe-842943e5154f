/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import { useCountries, useStates } from '@/services/geolocation/useGetJurisdiction';
import { newBuyerStore } from '@/store/newBuyer.store';
import { Country, State } from '@/types/newBuyer.types';
import axios from 'axios';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

export const useJurisdiction = () => {
  const { setJurisdiction, newBuyerData } = newBuyerStore();
  const [selectedCountry, setSelectedCountry] = useState<Country | null>(
    newBuyerData?.country || null,
  );
  const [selectedState, setSelectedState] = useState<State | null>(newBuyerData?.state || null);
  const [isGeoLookupLoading, setIsGeoLookupLoading] = useState(false);
  const router = useRouter();

  // React Query hooks to fetch data
  const {
    data: countries = [],
    isLoading: loadingCountries,
    error: countriesError,
  } = useCountries();

  const {
    data: states = [],
    isLoading: loadingStates,
    error: statesError,
  } = useStates(selectedCountry?.id);

  // To avoid repeated geo lookups: track if we've already tried
  const geoLookupTriedRef = useRef(false);

  function isCountry(country: any): country is Country {
    return (
      country &&
      typeof country.id === 'number' &&
      typeof country.name === 'string' &&
      typeof country.emoji === 'string' &&
      typeof country.currency === 'string' &&
      typeof country.phone_code === 'string' &&
      typeof country.iso3 === 'string' &&
      typeof country.iso2 === 'string'
    );
  }

  // Auto-detect user's location when countries are loaded, but only once
  useEffect(() => {
    if (!geoLookupTriedRef.current && selectedCountry?.id === 0 && countries.length > 0) {
      geoLookupTriedRef.current = true;
      setIsGeoLookupLoading(true);
      axios
        .get('https://ipapi.co/json/')
        .then((response) => {
          const geo = response.data;
          const found = countries.find((c) => c.name === geo.country_name);
          if (found && isCountry(found)) {
            setSelectedCountry(found);
          }
        })
        .catch((err) => {
          console.warn('Geo lookup failed', err);
          // Set India as default country when geo lookup fails
          const indiaCountry = countries.find((c) => c.name === 'India');
          if (indiaCountry && isCountry(indiaCountry)) {
            setSelectedCountry(indiaCountry);
          }
        })
        .finally(() => {
          setIsGeoLookupLoading(false);
        });
    }
  }, [countries, selectedCountry, isGeoLookupLoading]);

  // Reset selectedState when selectedCountry changes
  useEffect(() => {
    if (selectedCountry) {
      // If current selectedState is not in new list of states, clear it
      if (selectedState && !states.some((state) => Number(state.id) === Number(selectedState.id))) {
        setSelectedState(null);
      }
    } else {
      // If no country selected, clear state
      setSelectedState(null);
    }
  }, [selectedCountry, states, selectedState]);

  const getStateLabel = (countryName: string) => {
    switch (countryName) {
      case 'India':
        return 'State/Union Territory';
      case 'UAE':
        return 'Emirate';
      case 'Australia':
        return 'State/Territory';
      case 'USA':
        return 'State';
      default:
        return 'State/Province';
    }
  };

  const validateForm = () => !!(selectedCountry && selectedState);

  const onJurisdictionSubmit = () => {
    if (selectedCountry && selectedState) {
      setJurisdiction({
        country: selectedCountry,
        state: selectedState,
      });
      router.push('/buyer/org-type');
    }
  };

  return {
    countries,
    states,
    selectedCountry,
    selectedState,
    loadingCountries,
    loadingStates,
    isGeoLookupLoading,
    countriesError,
    statesError,
    setSelectedCountry,
    setSelectedState,
    getStateLabel,
    validateForm,
    onJurisdictionSubmit,
  };
};
