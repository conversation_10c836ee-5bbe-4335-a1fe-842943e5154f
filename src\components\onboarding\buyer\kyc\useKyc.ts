/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { FormioSchema } from '@/utils/formio/types';

const fetchKycFormJson = async (formId: number): Promise<FormioSchema> => {
  // Simulate API delay with Promise
  return new Promise((resolve) => {
    setTimeout(() => {
      const json: FormioSchema = {
        type: 'form',
        title: 'Individual KYC Verification',
        components: [
          {
            type: 'columns',
            columns: [
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 12,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'full_name',
                    type: 'textfield',
                    input: true,
                    label: 'Full Name',
                    validate: {
                      required: true,
                      minLength: 2,
                      maxLength: 100,
                      pattern: '^[a-zA-Z\\s\\.]+$',
                      patternMessage: 'Please enter a valid name (letters, spaces, and dots only)',
                    },
                    properties: {
                      key: 'full_name',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    placeholder: 'Enter your full legal name',
                  },
                ],
              },
            ],
            properties: {},
          },
          {
            type: 'columns',
            columns: [
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 6,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'dob',
                    type: 'datetime',
                    input: true,
                    label: 'Date of Birth',
                    format: 'yyyy-MM-dd',
                    enableDate: true,
                    enableTime: false,
                    validate: {
                      required: true,
                    },
                    properties: {
                      key: 'dob',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    placeholder: 'Select your date of birth',
                    description: 'You must be at least 18 years old',
                  },
                ],
              },
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 6,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'id_type',
                    type: 'select',
                    input: true,
                    label: 'ID Document Type',
                    validate: {
                      required: true,
                    },
                    data: {
                      values: [
                        {
                          label: 'Aadhaar Card',
                          value: 'aadhaar',
                        },
                        {
                          label: 'PAN Card',
                          value: 'pan',
                        },
                        {
                          label: 'Passport',
                          value: 'passport',
                        },
                        {
                          label: 'Driving License',
                          value: 'driving_license',
                        },
                        {
                          label: 'Voter ID',
                          value: 'voter_id',
                        },
                      ],
                    },
                    properties: {
                      key: 'id_type',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    placeholder: 'Select your ID document type',
                  },
                ],
              },
            ],
            properties: {},
          },
          {
            type: 'columns',
            columns: [
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 12,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'id_number',
                    type: 'textfield',
                    input: true,
                    label: 'ID Document Number',
                    validate: {
                      required: true,
                      minLength: 6,
                      maxLength: 20,
                      pattern: '^[A-Z0-9]+$',
                      patternMessage:
                        'Please enter a valid ID number (uppercase letters and numbers only)',
                    },
                    properties: {
                      key: 'id_number',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    placeholder: 'Enter your ID document number',
                  },
                ],
              },
            ],
            properties: {},
          },
          {
            type: 'columns',
            columns: [
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 6,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'id_upload',
                    type: 'file',
                    input: true,
                    label: 'ID Document Upload',
                    validate: {
                      required: true,
                    },
                    file: {
                      name: 'id_document',
                      url: '/api/upload',
                      options: {
                        withCredentials: false,
                      },
                    },
                    fileTypes: [
                      {
                        label: 'Image',
                        value: 'image/*',
                      },
                      {
                        label: 'PDF',
                        value: 'application/pdf',
                      },
                    ],
                    properties: {
                      key: 'id_upload',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    description: 'Upload a clear image or PDF of your ID document (max 5MB)',
                  },
                ],
              },
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 6,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'selfie_upload',
                    type: 'file',
                    input: true,
                    label: 'Selfie Upload',
                    validate: {
                      required: true,
                    },
                    file: {
                      name: 'selfie_photo',
                      url: '/api/upload',
                      options: {
                        withCredentials: false,
                      },
                    },
                    fileTypes: [
                      {
                        label: 'Image',
                        value: 'image/*',
                      },
                    ],
                    properties: {
                      key: 'selfie_upload',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    description: 'Upload a clear selfie photo holding your ID document (max 5MB)',
                  },
                ],
              },
            ],
            properties: {},
          },
          {
            type: 'columns',
            columns: [
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 12,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'res_address',
                    type: 'textarea',
                    input: true,
                    label: 'Residential Address',
                    validate: {
                      required: true,
                      minLength: 10,
                      maxLength: 500,
                    },
                    properties: {
                      key: 'res_address',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                    placeholder:
                      'Enter your complete residential address including city, state, and postal code',
                    rows: 4,
                  },
                ],
              },
            ],
            properties: {},
          },
          {
            type: 'columns',
            columns: [
              {
                pull: 0,
                push: 0,
                size: 'md',
                width: 12,
                offset: 0,
                components: [
                  {
                    disabled: false,
                    key: 'kyc_consent',
                    type: 'checkbox',
                    input: true,
                    label:
                      'I consent to the processing of my personal data for KYC verification purposes and confirm that all information provided is accurate',
                    validate: {
                      required: true,
                    },
                    defaultValue: false,
                    properties: {
                      key: 'kyc_consent',
                      required: true,
                      expectedMultipleUsage: false,
                    },
                  },
                ],
              },
            ],
            properties: {},
          },
        ],
      };

      resolve(json);
    }, 500);
  });
};

export const useKyc = () => {
  const params = useParams();
  const router = useRouter();
  const [formJson, setFormJson] = useState<FormioSchema>({
    type: 'form',
    title: '',
    components: [],
  });
  const [isFormLoading, setIsFormLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    //call API get form JSON from backend
    const formId = typeof params.id === 'string' ? parseInt(params.id, 10) : 0;
    const fetchForm = async () => {
      const json = await fetchKycFormJson(formId);
      setFormJson(json);
      setIsFormLoading(false);
    };
    fetchForm();
  }, [params.id]);

  const handleFormSubmit = async (data: any) => {
    try {
      setIsSubmitting(true);

      // Simulate validation
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Simulate file upload
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Simulate API submission
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Save KYC data in store (simulated)
      // newBuyerStore.setState({ kycData: data });

      setIsSubmitting(false);
      setIsSubmitted(true);

      // Wait 5 seconds then redirect
      setTimeout(() => {
        setIsRedirecting(true);
        setTimeout(() => {
          router.push('/buyer/sourcing-intent');
        }, 1000); // Additional 1 second for redirecting loader
      }, 5000);
    } catch (error) {
      console.error('Error submitting KYC form:', error);
      setIsSubmitting(false);
      // Here you might want to show an error message to the user
    }
  };

  return {
    formJson,
    isFormLoading,
    isSubmitting,
    isSubmitted,
    isRedirecting,
    handleFormSubmit,
  };
};
