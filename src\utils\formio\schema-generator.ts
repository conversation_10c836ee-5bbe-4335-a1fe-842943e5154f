/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { z } from 'zod';
import type { FormioComponent, FormioColumnsComponent } from './types';

/**
 * Generates a Zod validation schema based on Form.io components.
 */
export const createZodSchema = (components: Array<FormioComponent | FormioColumnsComponent>) => {
  const fields: Record<string, z.ZodTypeAny> = {};

  const handleField = (comp: FormioComponent) => {
    if (!comp.key || !comp.input) return;
    const v = comp.validate || {};
    const opts = comp.data?.values ?? comp.values;

    let schema: z.ZodTypeAny;

    switch (comp.type) {
      case 'textfield':
      case 'textarea':
        schema = z.string();
        break;
      case 'email':
        schema = z.string().email('Invalid email address');
        break;
      case 'number':
        schema = z.number();
        break;
      case 'datetime':
        schema = z.string().refine((val) => !isNaN(Date.parse(val)), {
          message: `${comp.label || comp.key} must be a valid date`,
        });
        break;
      case 'select':
      case 'radio':
        schema = z.string();
        break;
      case 'checkbox':
        if (opts && opts.length > 0) {
          // checkbox group
          schema = z.array(z.string());
        } else {
          // single checkbox
          schema = z.boolean();
        }
        break;
      case 'file':
        // Validate file uploads: must be File, allowed types, max size
        schema = z
          .any()
          .refine((val) => val instanceof File, {
            message: `${comp.label || comp.key} must be a file`,
          })
          .refine((file) => (file as File).size <= 5 * 1024 * 1024, {
            message: `${comp.label || comp.key} must be ≤ 5 MB`,
          })
          .refine(
            (file) =>
              ['image/png', 'image/jpeg', 'image/jpg', 'application/pdf'].includes(
                (file as File).type,
              ),
            {
              message: `${comp.label || comp.key} must be JPG, PNG or PDF`,
            },
          );
        break;

      default:
        schema = z.any();
    }

    // required
    if (v.required) {
      if (comp.type === 'checkbox' && opts && opts.length > 0) {
        schema = (schema as z.ZodArray<z.ZodString>).min(1, `Required`);
      } else if (comp.type === 'checkbox') {
        schema = (schema as z.ZodBoolean).refine((val) => val === true, {
          message: `Required`,
        });
      } else if (schema instanceof z.ZodString) {
        schema = schema.min(1, `Required`);
      }
    } else {
      schema = schema.optional();
    }

    // string patterns
    if (v.pattern && schema instanceof z.ZodString) {
      const rx = new RegExp(v.pattern);
      schema = schema.refine((str) => !str || rx.test(str), {
        message: v.patternMessage || 'Invalid format',
      });
    }

    // lengths
    if (v.minLength && schema instanceof z.ZodString) {
      schema = schema.min(v.minLength, `Minimum ${v.minLength} characters`);
    }
    if (v.maxLength && schema instanceof z.ZodString) {
      schema = schema.max(v.maxLength, `Maximum ${v.maxLength} characters`);
    }

    // number range
    if (comp.type === 'number' && schema instanceof z.ZodNumber) {
      if (v.min !== undefined)
        schema = (schema as z.ZodNumber).min(v.min, `Minimum value is ${v.min}`);
      if (v.max !== undefined)
        schema = (schema as z.ZodNumber).max(v.max, `Maximum value is ${v.max}`);
    }

    fields[comp.key] = schema;
  };

  const recurse = (comps: Array<FormioComponent | FormioColumnsComponent>) => {
    comps.forEach((c) => {
      if (c.type === 'columns') {
        (c as FormioColumnsComponent).columns.forEach((col) => recurse(col.components));
      } else if ((c as FormioComponent).components) {
        recurse((c as FormioComponent).components!);
      } else {
        handleField(c as FormioComponent);
      }
    });
  };

  recurse(components);
  return z.object(fields);
};

/**
 * Builds default values for React Hook Form based on Form.io components.
 */
export const getDefaultValues = (components: Array<FormioComponent | FormioColumnsComponent>) => {
  const defaults: Record<string, any> = {};

  const handleDefault = (comp: FormioComponent) => {
    if (!comp.key || !comp.input) return;
    if (comp.defaultValue !== undefined) {
      defaults[comp.key] = comp.defaultValue;
      return;
    }
    // set sensible defaults for checkbox groups
    if (comp.type === 'checkbox' && (comp.data?.values ?? comp.values)?.length) {
      defaults[comp.key] = [];
    }
  };

  const recurse = (comps: Array<FormioComponent | FormioColumnsComponent>) => {
    comps.forEach((c) => {
      if (c.type === 'columns') {
        (c as FormioColumnsComponent).columns.forEach((col) => recurse(col.components));
      } else if ((c as FormioComponent).components) {
        recurse((c as FormioComponent).components!);
      } else {
        handleDefault(c as FormioComponent);
      }
    });
  };

  recurse(components);
  return defaults;
};
