'use client';

import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  <PERSON><PERSON><PERSON>le,
  CardContent,
  Button,
  Badge,
} from '@kratex-tradetech/kratex-ui-library';
import { Edit, Users } from 'lucide-react';

interface TeamMember {
  id: string;
  full_name: string;
  invite_email: string;
  phone_number: string;
  seat_role: string;
}

interface TeamMembersProps {
  data: TeamMember[];
  onEdit: (section: string) => void;
}

const TeamMembers: React.FC<TeamMembersProps> = ({ data, onEdit }) => {
  const seatRoleLabels: Record<string, string> = {
    PROCUREMENT: 'Procurement',
    FINANCE: 'Finance',
    ADMIN: 'Admin',
    MANAGER: 'Manager',
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Users className="h-4 w-4" />
            Team Members ({data.length})
          </CardTitle>
          <Button variant="outline" size="sm" onClick={() => onEdit('team')}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {data.length > 0 ? (
          <div className="space-y-3">
            {data.map((member, _index) => (
              <div
                key={member.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="space-y-1">
                  <p className="text-sm font-medium">{member.full_name}</p>
                  <p className="text-xs text-gray-600">{member.invite_email}</p>
                  <p className="text-xs text-gray-600">{member.phone_number}</p>
                </div>
                <Badge variant="outline">{seatRoleLabels[member.seat_role]}</Badge>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-sm text-gray-500">No team members added</p>
        )}
      </CardContent>
    </Card>
  );
};

export default TeamMembers;
