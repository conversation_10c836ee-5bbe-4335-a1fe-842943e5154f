import { useAuthState } from '@/utils/withRoleAccess';
import { useRouter } from 'next/navigation';

export const useSupplierDashboard = () => {
  const router = useRouter();
  const { user } = useAuthState();
  // Get user initials for avatar fallback
  const getUserInitials = (name: string): string => {
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return {
    user,
    router,
    getUserInitials,
  };
};
