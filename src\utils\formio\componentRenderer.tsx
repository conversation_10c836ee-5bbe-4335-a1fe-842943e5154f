'use client';

import { useState } from 'react';
import type { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@kratex-tradetech/kratex-ui-library';
import { Input } from '@kratex-tradetech/kratex-ui-library';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@kratex-tradetech/kratex-ui-library';
import { Textarea } from '@kratex-tradetech/kratex-ui-library';
import { Checkbox } from '@kratex-tradetech/kratex-ui-library';
import { RadioGroup, RadioGroupItem } from '@kratex-tradetech/kratex-ui-library';
import type { FormioComponent } from './types';
import { Popover, PopoverTrigger } from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle, ChevronDownIcon, FileCheck, Info, Mail, Upload, X } from 'lucide-react';
import { Button } from '@kratex-tradetech/kratex-ui-library';
import { PopoverContent } from '@kratex-tradetech/kratex-ui-library';
import { Calendar } from '@kratex-tradetech/kratex-ui-library';
import { InFormEmailVerification } from '@kratex-tradetech/kratex-ui-library';
import { newBuyerStore } from '@/store/newBuyer.store';
import { toast } from 'sonner';

interface FormioComponentRendererProps {
  component: FormioComponent;
  form: UseFormReturn<any>;
}

export const FormioComponentRenderer: React.FC<FormioComponentRendererProps> = ({
  component,
  form,
}) => {
  const { newBuyerData, setEmailVerified } = newBuyerStore();
  // Get current email from accountBasics or default to empty string
  const currentEmail = newBuyerData?.accountBasics?.email || '';
  const [localEmail, setLocalEmail] = useState(currentEmail);
  const [open, setOpen] = useState(false);
  const renderInput = () => {
    const options =
      component.data?.values ??
      // fallback for radio definitions that use `values` instead of `data.values`
      (component.values as Array<{ label: string; value: string }> | undefined) ??
      [];

    switch (component.type) {
      case 'textfield':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{component.label}</FormLabel>
                <FormControl>
                  <Input
                    placeholder={component.placeholder}
                    {...field}
                    value={field.value || ''}
                    disabled={component.disabled}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'textarea':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{component.label}</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={component.placeholder}
                    {...field}
                    value={field.value || ''}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'email':
        if (newBuyerData?.isEmailVerified) {
          return (
            <FormField
              control={form.control}
              name={component.key!}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{component.label}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        type="email"
                        placeholder={component.placeholder}
                        {...field}
                        value={newBuyerData?.accountBasics?.email || field.value || ''}
                        readOnly
                        className="pl-10 text-gray-500 bg-gray-50 cursor-not-allowed"
                      />
                      <CheckCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-green-500" />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          );
        } else {
          return (
            <InFormEmailVerification
              email={localEmail}
              onEmailChange={(email: string) => {
                setLocalEmail(email);
                form.setValue(component.key!, email);
              }}
              onVerificationComplete={(data: any) => {
                // Handle verification completion
                // console.log('Email verification completed:', data);
                setEmailVerified(true);
                toast.success('Email verified successfully!');
              }}
              onVerificationStart={(
                data: any,
              ) => {
                // console.log('Email verification started:', data, newBuyerData.isEmailVerified);
              }}
              // not implemented
              sendOTPFunction={() => {}}
              verifyOTPFunction={() => {}}
              style="default"
            />
          );
        }

      case 'number':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{component.label}</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder={component.placeholder}
                    {...field}
                    value={field.value || ''}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'select':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{component.label}</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  value={field.value || component.defaultValue}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={component.placeholder} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {component.data?.values?.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'radio':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>{component.label}</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    value={field.value}
                    className="flex flex-col space-y-1"
                  >
                    {options.map((opt) => (
                      <div key={opt.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={opt.value} id={`${component.key}-${opt.value}`} />
                        <label htmlFor={`${component.key}-${opt.value}`} className="text-sm">
                          {opt.label}
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case 'checkbox':
        if (options.length > 0) {
          return (
            <FormField
              control={form.control}
              name={component.key!}
              render={({ field }) => {
                // ensure value is an array
                const selected: string[] = Array.isArray(field.value) ? field.value : [];
                const toggle = (val: string) => {
                  const next = selected.includes(val)
                    ? selected.filter((v) => v !== val)
                    : [...selected, val];
                  field.onChange(next);
                };

                return (
                  <FormItem className="space-y-2">
                    <FormLabel>{component.label}</FormLabel>
                    <FormControl>
                      <div className="flex flex-col space-y-1">
                        {options.map((opt) => (
                          <div key={opt.value} className="flex items-center space-x-2">
                            <Checkbox
                              checked={selected.includes(opt.value)}
                              onCheckedChange={() => toggle(opt.value)}
                              id={`${component.key}-${opt.value}`}
                            />
                            <label htmlFor={`${component.key}-${opt.value}`} className="text-sm">
                              {opt.label}
                            </label>
                          </div>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                );
              }}
            />
          );
        }

        // otherwise treat it as a single boolean checkbox
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem className="flex items-start space-x-2">
                <FormControl>
                  <Checkbox
                    id={component.key}
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div>
                  <FormLabel htmlFor={component.key}>{component.label}</FormLabel>
                  {component.description && (
                    <FormDescription>{component.description}</FormDescription>
                  )}
                  <FormMessage />
                </div>
              </FormItem>
            )}
          />
        );

      case 'datetime':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => {
              return (
                <FormItem>
                  <FormLabel>{component.label}</FormLabel>
                  <FormControl>
                    <Popover open={open} onOpenChange={setOpen}>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-between font-normal">
                          {field.value
                            ? new Date(field.value).toLocaleDateString()
                            : component.placeholder || 'Select date'}
                          <ChevronDownIcon className="ml-2 h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 bg-white border shadow-md rounded-lg"
                        align="start"
                      >
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date(field.value) : undefined}
                          captionLayout="dropdown"
                          fromYear={1900}
                          toYear={new Date().getFullYear()}
                          classNames={{
                            months:
                              'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0 p-3',
                            month: 'space-y-4',
                            caption: 'flex justify-center pt-1 relative items-center gap-1',
                            caption_label: 'hidden',
                            caption_dropdowns: 'flex justify-center gap-1',
                            dropdown_month:
                              'relative inline-flex items-center rounded-md border-0 bg-transparent px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:cursor-not-allowed disabled:opacity-50 [&>select]:border-0 [&>select]:bg-transparent [&>select]:outline-none [&>select]:focus:outline-none',
                            dropdown_year:
                              'relative inline-flex items-center rounded-md border-0 bg-transparent px-3 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:outline-none focus:bg-accent focus:text-accent-foreground disabled:cursor-not-allowed disabled:opacity-50 [&>select]:border-0 [&>select]:bg-transparent [&>select]:outline-none [&>select]:focus:outline-none',
                            nav: 'space-x-1 flex items-center',
                            nav_button:
                              'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-7 w-7',
                            nav_button_previous: 'absolute left-1',
                            nav_button_next: 'absolute right-1',
                            table: 'w-full border-collapse space-y-1',
                            head_row: 'flex',
                            head_cell:
                              'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',
                            row: 'flex w-full mt-2',
                            cell: 'h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20',
                            day: 'inline-flex items-center justify-center rounded-md text-sm font-normal ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-9 w-9 p-0 font-normal aria-selected:opacity-100',
                            day_range_end: 'day-range-end',
                            day_selected:
                              'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
                            day_today: 'bg-accent text-accent-foreground',
                            day_outside:
                              'day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30',
                            day_disabled: 'text-muted-foreground opacity-50',
                            day_range_middle:
                              'aria-selected:bg-accent aria-selected:text-accent-foreground',
                            day_hidden: 'invisible',
                          }}
                          onSelect={(date) => {
                            const iso = date ? date.toISOString() : '';
                            field.onChange(iso);
                            setOpen(false);
                          }}
                        />
                      </PopoverContent>
                    </Popover>
                  </FormControl>
                  {component.description && (
                    <FormDescription>{component.description}</FormDescription>
                  )}
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        );

      case 'file':
        return (
          <FormField
            control={form.control}
            name={component.key!}
            render={({ field }) => (
              <FormItem>
                <FormLabel>{component.label}</FormLabel>
                <FormControl>
                  <div className="relative">
                    <label
                      htmlFor={`file-upload-${component.key}`}
                      className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors group"
                    >
                      <input
                        id={`file-upload-${component.key}`}
                        type="file"
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        onChange={(e) => field.onChange(e.target.files?.[0] ?? null)}
                      />
                      <div className="flex flex-col items-center space-y-2 text-gray-500 group-hover:text-gray-600">
                        {field.value ? (
                          <>
                            <FileCheck className="w-8 h-8" />
                            <div className="text-center">
                              <p className="text-sm font-medium">{(field.value as File).name}</p>
                              <p className="text-xs">
                                {((field.value as File).size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          </>
                        ) : (
                          <>
                            <Upload className="w-8 h-8" />
                            <div className="text-center">
                              <p className="text-sm font-medium">Click to upload file</p>
                              <p className="text-xs">or drag and drop</p>
                            </div>
                          </>
                        )}
                      </div>
                    </label>

                    {field.value && (
                      <button
                        type="button"
                        onClick={() => field.onChange(null)}
                        className="absolute -top-2 -right-2 w-6 h-6 bg-white border border-gray-300 rounded-full flex items-center justify-center hover:bg-gray-50 transition-colors"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                </FormControl>
                {component.description && (
                  <FormDescription className="flex items-center space-x-1">
                    <Info className="w-3 h-3" />
                    <span>{component.description}</span>
                  </FormDescription>
                )}
                <FormMessage />
              </FormItem>
            )}
          />
        );

      default:
        return (
          <div className="p-2 border border-dashed border-gray-300 rounded">
            <span className="text-sm text-gray-500">
              Unsupported component type: {component.type}
            </span>
          </div>
        );
    }
  };

  return renderInput();
};
