import AppConfigInitializer from '@/components/AppConfigInitializer';
import { Toaster } from '@/components/common/Toaster';
import QueryProvider from '@/providers/query-provider';
import '@kratex-tradetech/kratex-ui-library/styles';
import type { Metadata } from 'next';
import { Gei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Kratex Tradetech',
  description: 'Generated by create next app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <QueryProvider>
          <AppConfigInitializer>{children}</AppConfigInitializer>
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  );
}
