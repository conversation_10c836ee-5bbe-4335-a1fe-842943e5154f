'use client';

import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardTitle,
  CardDescription,
  CardContent,
  Button,
  Separator,
} from '@kratex-tradetech/kratex-ui-library';
import { CheckCircle } from 'lucide-react';
import { useReviewOnboarding } from './useFinalize';
import { withBuyerRoute } from '@/utils/withRoleAccess';

// Component imports
import LoadingState from './LoadingState';
import EmptyState from './EmptyState';
import PersonalInfo from './PersonalInfoCard';
import OrganizationInfo from './OrganisationInfo';
import SourcingIntent from './SourcingIntent';
import TeamMembers from './TeamMembers';
import KycStatus from './KycStatus';

const ReviewOnboarding = () => {
  const { onboardingData, isLoading, isSubmitting, handleConfirmFinish } = useReviewOnboarding();

  const handleEdit = (_section: string) => {
    // Navigation logic would go here
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (!onboardingData) {
    return <EmptyState />;
  }

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Review and Finalize
            </CardTitle>
            <CardDescription>
              Please review all the information you have provided before finalizing your account
              setup.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <PersonalInfo data={onboardingData.personal_info} onEdit={handleEdit} />

            <OrganizationInfo data={onboardingData.organization_info} onEdit={handleEdit} />

            <SourcingIntent data={onboardingData.sourcing_intent} onEdit={handleEdit} />

            <TeamMembers data={onboardingData.team_members} onEdit={handleEdit} />

            <KycStatus status={onboardingData.kyc_status} />

            <Separator />

            {/* Confirm Button */}
            <div className="flex justify-center pt-4">
              <Button
                onClick={handleConfirmFinish}
                disabled={isSubmitting}
                size="lg"
                className="min-w-48"
              >
                {isSubmitting ? 'Creating Account...' : 'Confirm & Finish'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default withBuyerRoute(ReviewOnboarding);
