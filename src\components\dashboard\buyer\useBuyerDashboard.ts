import { useState, useRef, useEffect } from 'react';
import { useAuthState } from '@/utils/withRoleAccess';
import { useRouter } from 'next/navigation';
import { userStore } from '@/store/user.store';
import { newBuyerStore } from '@/store/newBuyer.store';
import { clearTokenFromCookie } from '@/lib/cookies';

export const useBuyerDashboard = () => {
  const router = useRouter();
  const { user } = useAuthState();

  // Avatar popup state and ref
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const popupRef = useRef<HTMLDivElement>(null);

  // Close popup when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popupRef.current && !popupRef.current.contains(event.target as Node)) {
        setIsPopupOpen(false);
      }
    };

    if (isPopupOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isPopupOpen]);

  // Get user initials for avatar fallback
  const getUserInitials = (name: string): string => {
    return name
      .split(' ')
      .map((word) => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Handle avatar click
  const handleAvatarClick = () => {
    setIsPopupOpen(!isPopupOpen);
  };

  // Logout function
  const logout = () => {
    // Clear Zustand stores
    userStore.getState().clearUser();
    newBuyerStore.getState().clearNewBuyerData();
    clearTokenFromCookie();
    // Redirect to login
    window.location.href = '/login-signup';
  };

  // Handle logout with popup close
  const handleLogout = () => {
    logout();
    setIsPopupOpen(false);
  };

  return {
    user,
    router,
    getUserInitials,
    logout,
    // Avatar popup related
    isPopupOpen,
    popupRef,
    handleAvatarClick,
    handleLogout,
  };
};
