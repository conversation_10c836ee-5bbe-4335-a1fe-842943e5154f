'use client';
import { <PERSON><PERSON>, <PERSON>, CardContent } from '@kratex-tradetech/kratex-ui-library';
import { FormioFormRenderer } from '@/utils/formio/form-renderer';
import { useRouter } from 'next/navigation';
import { useKyc } from './useKyc';
import { withBuyerRoute } from '@/utils/withRoleAccess';

const KYCPage = () => {
  const router = useRouter();
  const { formJson, isFormLoading, isSubmitting, isSubmitted, isRedirecting, handleFormSubmit } =
    useKyc();

  const handleSkipKYC = () => {
    router.push('/buyer/sourcing-intent');
  };

  // Show skip button only when form is loaded and not in any processing state
  const showSkipButton = !isFormLoading && !isSubmitting && !isSubmitted && !isRedirecting;

  if (isFormLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p>Loading KYC form...</p>
        </div>
      </div>
    );
  }

  if (isSubmitting) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p>Saving your data...</p>
        </div>
      </div>
    );
  }

  if (isRedirecting) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2"></div>
          <p>Redirecting...</p>
        </div>
      </div>
    );
  }

  if (isSubmitted) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <div className="mb-4">
              <div className="w-12 h-12 rounded-full bg-black flex items-center justify-center mx-auto mb-3">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Data Submitted Successfully</h3>
              <p className="text-gray-600">
                Data sent for verification, soon the status will reflect on your dashboard
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto px-4">
      {/* Header with Skip Button */}
      <div className="text-center mb-6">
        <div className="flex justify-center items-start gap-4 mb-4">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">KYC</h1>
            <p className="text-gray-600 mt-1">
              Complete your KYC verification to continue with your account setup
            </p>
          </div>
        </div>
      </div>

      {/* Form Card */}
      <Card className="border shadow">
        <CardContent className="p-6">
          <FormioFormRenderer formioJson={formJson} onSubmit={handleFormSubmit} showCard={false} />
        </CardContent>
        {showSkipButton && (
          <Button onClick={handleSkipKYC} variant="outline" className="flex-shrink-0 mt-1">
            Skip KYC
          </Button>
        )}
      </Card>
    </div>
  );
};

export default withBuyerRoute(KYCPage);
