'use client';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Label,
  Button,
  Input,
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@kratex-tradetech/kratex-ui-library';
import { Trash2, Plus, UserPlus, ChevronDown } from 'lucide-react';
import { useInviteTeam } from './useInviteTeam';
import { SeatRole } from './types';
import { withBuyerRoute } from '@/utils/withRoleAccess';

const InviteTeamMembers = () => {
  const {
    invites,
    errors,
    seatRoleOptions,
    addInviteRow,
    removeInviteRow,
    updateInvite,
    updateInviteRoles,
    handleSendInvites,
    handleSkip,
    getRoleLabels,
    getSelectedRolesDisplay,
  } = useInviteTeam();

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Invite Team Members
            </CardTitle>
            <CardDescription>
              Add team members to your account. This step is optional and can be completed later.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSendInvites();
              }}
              className="space-y-4"
            >
              {invites.map((invite, index) => (
                <Card key={invite.id} className="relative">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Team Member {index + 1}</CardTitle>
                      {invites.length > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeInviteRow(invite.id)}
                          className="h-8 w-8 p-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Full Name */}
                      <div className="space-y-2">
                        <Label htmlFor={`full-name-${invite.id}`}>Full Name *</Label>
                        <Input
                          id={`full-name-${invite.id}`}
                          type="text"
                          placeholder="Enter full name"
                          value={invite.full_name}
                          onChange={(e) => updateInvite(invite.id, 'full_name', e.target.value)}
                        />
                        {errors[invite.id]?.full_name && (
                          <p className="text-sm text-red-500">{errors[invite.id].full_name}</p>
                        )}
                      </div>

                      {/* Phone Number */}
                      <div className="space-y-2">
                        <Label htmlFor={`phone-${invite.id}`}>Phone Number *</Label>
                        <Input
                          id={`phone-${invite.id}`}
                          type="tel"
                          placeholder="Enter phone number"
                          value={invite.phone_number}
                          onChange={(e) => updateInvite(invite.id, 'phone_number', e.target.value)}
                        />
                        {errors[invite.id]?.phone_number && (
                          <p className="text-sm text-red-500">{errors[invite.id].phone_number}</p>
                        )}
                      </div>

                      {/* Email */}
                      <div className="space-y-2">
                        <Label htmlFor={`email-${invite.id}`}>Email Address *</Label>
                        <Input
                          id={`email-${invite.id}`}
                          type="email"
                          placeholder="Enter email address"
                          value={invite.invite_email}
                          onChange={(e) => updateInvite(invite.id, 'invite_email', e.target.value)}
                        />
                        {errors[invite.id]?.invite_email && (
                          <p className="text-sm text-red-500">{errors[invite.id].invite_email}</p>
                        )}
                      </div>

                      {/* Roles (multi-select) */}
                      <div className="space-y-2">
                        <Label htmlFor={`role-${invite.id}`}>Roles *</Label>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-between"
                              id={`role-${invite.id}`}
                            >
                              {getSelectedRolesDisplay(invite.seat_roles)}
                              <ChevronDown className="h-4 w-4 ml-2" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent>
                            <DropdownMenuLabel>Select Roles</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            {seatRoleOptions.map((option) => (
                              <DropdownMenuCheckboxItem
                                key={option.value}
                                checked={invite.seat_roles.includes(option.value)}
                                onCheckedChange={(checked) => {
                                  let newRoles: SeatRole[];
                                  if (checked) {
                                    newRoles = [...invite.seat_roles, option.value];
                                  } else {
                                    newRoles = invite.seat_roles.filter((r) => r !== option.value);
                                  }
                                  updateInviteRoles(invite.id, newRoles);
                                }}
                              >
                                {option.label}
                              </DropdownMenuCheckboxItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {invite.seat_roles.map((role) => (
                            <div key={role} className="bg-gray-100 rounded-md px-2 py-1 text-sm">
                              {getRoleLabels([role])}
                            </div>
                          ))}
                        </div>
                        {errors[invite.id]?.seat_roles && (
                          <p className="text-sm text-red-500">{errors[invite.id].seat_roles}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Add More Button */}
              <Button variant="outline" onClick={addInviteRow} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Add Another Team Member
              </Button>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 pt-6">
                <Button type="submit" onClick={handleSendInvites} className="flex-1">
                  Send Invitations
                </Button>
                <Button type="button" variant="outline" onClick={handleSkip} className="flex-1">
                  Skip for Now
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default withBuyerRoute(InviteTeamMembers);
