'use client';

import { Avatar, AvatarFallback, Button } from '@kratex-tradetech/kratex-ui-library';
import { Bell } from 'lucide-react';
import { useSupplierDashboard } from './useSupplierDashboard';
import { withSupplierRoute } from '@/utils/withRoleAccess';

const SupplierDashboard: React.FC = () => {
  const { getUserInitials, user } = useSupplierDashboard();

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <header className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Supplier Dashboard</h1>
            <p className="text-muted-foreground mt-1">
              Welcome back, {user?.GivenName || 'Supplier'}! Here&apos;s what&apos;s happening with
              your orders.
            </p>
          </div>

          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" aria-label="Notifications">
              <Bell className="h-4 w-4" />
            </Button>

            <Avatar>
              <AvatarFallback>{getUserInitials(user?.GivenName || 'Supplier')}</AvatarFallback>
            </Avatar>
          </div>
        </header>

        {/* Main Content */}
        <main className="space-y-6">
          {/* Add your dashboard content here */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Placeholder cards - replace with actual dashboard content */}
            <div className="p-6 bg-card rounded-lg border">
              <h3 className="font-semibold mb-2">Active Orders</h3>
              <p className="text-muted-foreground">Your current orders</p>
            </div>

            <div className="p-6 bg-card rounded-lg border">
              <h3 className="font-semibold mb-2">Recent Activity</h3>
              <p className="text-muted-foreground">Latest updates</p>
            </div>

            <div className="p-6 bg-card rounded-lg border">
              <h3 className="font-semibular mb-2">Quick Actions</h3>
              <p className="text-muted-foreground">Common tasks</p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default withSupplierRoute(SupplierDashboard);
