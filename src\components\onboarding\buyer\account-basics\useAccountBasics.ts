'use client';

import { useGetFormDefinition } from '@/services/forms/useGetFormDefinition';
import { useBuyerRegister } from '@/services/buyer/useRegisterBuyer';
import {
  newBuyerStore,
  useNewBuyerJurisdiction,
  useNewBuyerOrgType,
  useNewBuyerPhoneNo,
} from '@/store/newBuyer.store';
import { userStore } from '@/store/user.store';
import { injectPhoneNumberIntoSchema } from '@/utils/formio/formSchemaDataInjection';
import { useRouter } from 'next/navigation';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';

export const useAccountBasics = () => {
  const router = useRouter();

  const {
    data: formSchema,
    isLoading: schemaLoading,
    error: schemaError,
  } = useGetFormDefinition('accountBasicsBuyer');

  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: registerBuyer } = useBuyerRegister();
  const jurisdiction = useNewBuyerJurisdiction();
  const orgType = useNewBuyerOrgType();
  const { setAccountBasics } = newBuyerStore();
  const phoneNumber = useNewBuyerPhoneNo();

  // Transform the schema to make phone field disabled and pre-populated
  const transformedSchema = useMemo(() => {
    return formSchema?.schema
      ? injectPhoneNumberIntoSchema(formSchema.schema, phoneNumber)
      : undefined;
  }, [formSchema?.schema, phoneNumber]);

  const handleFormSubmit = async (formData: Record<string, unknown>) => {
    // Ensure phone number is included in form data
    const formDataWithPhone = {
      ...formData,
      phone: phoneNumber,
    };

    setAccountBasics(formDataWithPhone);
    setIsSubmitting(true);

    try {
      const payload = {
        organisation_type: orgType.id,
        jurisdiction: jurisdiction.state.id,
        email: formData.email,
        buyer_details: formDataWithPhone,
      };
      const response = await registerBuyer(payload);
      const { setUser } = userStore.getState();
      const displayName = formData.display_name?.toString() ?? 'Buyer';
      const email = formData.email?.toString() ?? '<EMAIL>';
      setUser({
        role: 2,
        GivenName: displayName,
        Surname: displayName,
        Email: email,
        isRegistered: true,
      });
      toast.success('Account created successfully!');
      router.push(`/buyer/kyc/${response.id}`);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An unknown error occurred.';
      toast.error(message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    formSchema: transformedSchema,
    schemaLoading,
    schemaError,
    isSubmitting,
    handleFormSubmit,
  };
};
