/* eslint-disable @typescript-eslint/no-unused-vars */
import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { FormErrors, SeatRole, TeamMemberInvite } from './types';

export const useInviteTeam = () => {
  const router = useRouter();

  const [invites, setInvites] = useState<TeamMemberInvite[]>([
    {
      id: '1',
      full_name: '',
      phone_number: '',
      invite_email: '',
      seat_roles: [],
    },
  ]);

  const [errors, setErrors] = useState<FormErrors>({});

  const seatRoleOptions: { value: SeatRole; label: string }[] = [
    { value: 'PROCUREMENT', label: 'Procurement' },
    { value: 'FINANCE', label: 'Finance' },
    { value: 'SUPPORT', label: 'Support' },
    { value: 'VIEW_ONLY', label: 'View Only' },
  ];

  const generateId = (): string => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  const addInviteRow = (): void => {
    const newInvite: TeamMemberInvite = {
      id: generateId(),
      full_name: '',
      phone_number: '',
      invite_email: '',
      seat_roles: [], // changed
    };
    setInvites((prev) => [...prev, newInvite]);
  };

  const removeInviteRow = (id: string): void => {
    if (invites.length > 1) {
      setInvites((prev) => prev.filter((invite) => invite.id !== id));
      // Remove errors for the deleted row
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[id];
        return newErrors;
      });
    }
  };

  const updateInvite = (
    id: string,
    field: keyof Omit<TeamMemberInvite, 'id' | 'seat_roles'>, // fields except id and seat_roles
    value: string,
  ): void => {
    setInvites((prev) =>
      prev.map((invite) => (invite.id === id ? { ...invite, [field]: value } : invite)),
    );

    // Clear error for this field when user starts typing
    if (errors[id]?.[field]) {
      setErrors((prev) => ({
        ...prev,
        [id]: {
          ...prev[id],
          [field]: undefined,
        },
      }));
    }
  };

  // New: update multiple roles
  const updateInviteRoles = (id: string, roles: SeatRole[]): void => {
    setInvites((prev) =>
      prev.map((invite) => (invite.id === id ? { ...invite, seat_roles: roles } : invite)),
    );
    // Clear error for seat_roles if exists
    if (errors[id]?.seat_roles) {
      setErrors((prev) => ({
        ...prev,
        [id]: {
          ...prev[id],
          seat_roles: undefined,
        },
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    invites.forEach((invite) => {
      const inviteErrors: FormErrors[string] = {};

      if (!invite.full_name.trim()) {
        inviteErrors.full_name = 'Full name is required';
        isValid = false;
      }

      if (!invite.phone_number.trim()) {
        inviteErrors.phone_number = 'Phone number is required';
        isValid = false;
      }

      if (!invite.invite_email.trim()) {
        inviteErrors.invite_email = 'Email is required';
        isValid = false;
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(invite.invite_email)) {
        inviteErrors.invite_email = 'Please enter a valid email';
        isValid = false;
      }

      // Validate multi-select roles
      if (!invite.seat_roles || invite.seat_roles.length === 0) {
        inviteErrors.seat_roles = 'At least one role is required';
        isValid = false;
      }

      if (Object.keys(inviteErrors).length > 0) {
        newErrors[invite.id] = inviteErrors;
      }
    });

    // Check for duplicate emails
    const emails = invites
      .map((invite) => invite.invite_email.toLowerCase().trim())
      .filter(Boolean);
    const duplicateEmails = emails.filter((email, index) => emails.indexOf(email) !== index);

    if (duplicateEmails.length > 0) {
      invites.forEach((invite) => {
        if (duplicateEmails.includes(invite.invite_email.toLowerCase().trim())) {
          if (!newErrors[invite.id]) newErrors[invite.id] = {};
          newErrors[invite.id].invite_email = 'Email must be unique';
          isValid = false;
        }
      });
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSendInvites = (): void => {
    if (validateForm()) {
      const validInvites = invites.filter(
        (invite) =>
          invite.full_name.trim() &&
          invite.phone_number.trim() &&
          invite.invite_email.trim() &&
          invite.seat_roles.length > 0,
      );
      // TODO: send `validInvites` to backend or whatever logic
      router.push('/buyer/finalize');
    }
  };

  const handleSkip = (): void => {
    router.push('/buyer/finalize');
  };

  const getSelectedRolesDisplay = (roles: SeatRole[]): string => {
    if (roles.length === 0) return 'Select roles...';
    if (roles.length === 1) {
      return seatRoleOptions.find((opt) => opt.value === roles[0])?.label || '';
    }
    return `${roles.length} roles selected`;
  };

  const getRoleLabels = (roles: SeatRole[]): string[] => {
    return roles
      .map((role) => seatRoleOptions.find((opt) => opt.value === role)?.label)
      .filter(Boolean) as string[];
  };

  return {
    invites,
    errors,
    seatRoleOptions,
    addInviteRow,
    removeInviteRow,
    updateInvite,
    updateInviteRoles,
    handleSendInvites,
    handleSkip,
    getRoleLabels,
    getSelectedRolesDisplay,
  };
};
