FROM node:20-alpine as builder

RUN mkdir -p /opt/app-root/src

WORKDIR /opt/app-root/src

ENV MODE=production

ADD package.json /opt/app-root/src/package.json
ADD pnpm-lock.yaml /opt/app-root/src/pnpm-lock.yaml

RUN corepack enable && \
    corepack prepare pnpm@latest-10 --activate

ARG NODE_ENV=production
ARG NEXT_TELEMETRY_DISABLED=1

RUN --mount=type=secret,id=node_token,env=NODE_AUTH_TOKEN \
    echo "@kratex-tradetech:registry=https://npm.pkg.github.com" > .npmrc && \
    echo "//npm.pkg.github.com/:_authToken=$NODE_AUTH_TOKEN" >> .npmrc && \
    echo "always-auth=true" >> .npmrc && \
    pnpm install --frozen-lockfile && rm -f .npmrc

ADD public /opt/app-root/src/public
ADD src /opt/app-root/src/src
ADD tsconfig.json /opt/app-root/src/tsconfig.json
ADD next.config.ts /opt/app-root/src/
ADD jest.config.ts /opt/app-root/src/
ADD postcss.config.mjs /opt/app-root/src/

RUN --mount=type=secret,id=api_url,env=NEXT_PUBLIC_API_BASE_URL \
    pnpm run build

FROM gcr.io/distroless/nodejs20-debian12:nonroot

WORKDIR /opt/app-root/src

COPY --from=builder --chown=nonroot --chmod=755 /opt/app-root/src/.next/cache ./.next/cache
COPY --from=builder --chown=root --chmod=005 /opt/app-root/src/.next/standalone ./
COPY --from=builder --chown=root --chmod=005 /opt/app-root/src/.next/static ./.next/static
COPY --from=builder --chown=root --chmod=005 /opt/app-root/src/public ./public

ENV NODE_ENV=production

EXPOSE 3000

ENV HOSTNAME="0.0.0.0"
CMD ["server.js"]
