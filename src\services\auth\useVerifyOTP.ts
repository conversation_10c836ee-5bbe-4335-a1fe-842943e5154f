import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { OTPVerifyPayload, OTPVerifyResponse } from '@/types/otp.types';
import { useMutation } from '@tanstack/react-query';

export const useVerifyOTP = () => {
  return useMutation({
    mutationFn: (payload: OTPVerifyPayload) => verifyOTP(payload),
  });
};

export async function verifyOTP(payload: OTPVerifyPayload): Promise<OTPVerifyResponse> {
  try {
    const response = await apiClient.post('/api/v1/users/otp-verify', payload);
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to verify OTP');
  }
}
