import { useState, useRef, useEffect } from 'react';
import { DropdownOption } from './types';

export const useMultiSelectDropdown = (
  options: DropdownOption[],
  selectedValues: DropdownOption[],
  onSelectionChange: (value: DropdownOption) => void,
) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  const filteredOptions = options.filter((option) =>
    option.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const showSearch = options.length > 5;

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && showSearch && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen, showSearch]);

  const handleOptionClick = (option: DropdownOption) => {
    onSelectionChange(option);
  };

  const handleRemoveTag = (value: DropdownOption, e: React.MouseEvent) => {
    e.stopPropagation();
    onSelectionChange(value);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleDropdownToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setSearchTerm('');
    }
  };

  return {
    isOpen,
    searchTerm,
    filteredOptions,
    showSearch,
    dropdownRef,
    searchInputRef,
    handleOptionClick,
    handleRemoveTag,
    handleSearchChange,
    handleDropdownToggle,
  };
};
