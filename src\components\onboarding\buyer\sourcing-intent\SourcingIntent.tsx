'use client';
import {
  Button,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Checkbox,
} from '@kratex-tradetech/kratex-ui-library';
import { useSourcingIntent } from '@/components/onboarding/buyer/sourcing-intent/useSourcingIntent';
import { withBuyerRoute } from '@/utils/withRoleAccess';
import CheckboxGroup from '../../../common/CheckboxGroup/CheckboxGroup';
import MultiSelectDropdown from '../../../common/MultiSelectDropdown/MultiSelectDropdown';

const SourcingIntentForm = () => {
  const {
    formData,
    countries,
    useCases,
    productCategories,
    complianceOptions,
    shippingTerms,
    leadTimeOptions,
    loadingCountries,
    NO_PREFERENCE_OPTION,
    handleDropdownSelect,
    handleCheckboxChange,
    handleProductCategoryChange,
    handleSubmit,
    handleShippingTermChange,
    handleLeadTimeSelect,
  } = useSourcingIntent();

  const countryOptions = countries.map((country) => ({
    id: country.id,
    name: country.name,
  }));

  const sourcingFromOptions = [NO_PREFERENCE_OPTION, ...countryOptions];

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Define Sourcing Intent</CardTitle>
            <CardDescription>
              Provide information about your sourcing preferences and requirements to personalise
              your catalogue.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                handleSubmit();
              }}
              className="space-y-6"
            >
              {/* Sourcing For - Multi-Select Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="sourcing-for" className="text-base font-medium">
                  Sourcing For
                </Label>
                <MultiSelectDropdown
                  placeholder="Select countries to source for"
                  options={countryOptions}
                  selectedValues={formData.sourcing_for}
                  onSelectionChange={(value) => handleDropdownSelect('sourcing_for', value)}
                  isDataLoading={loadingCountries}
                  id="sourcing-for"
                />
              </div>

              {/* Sourcing From - Multi-Select Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="sourcing-from" className="text-base font-medium">
                  Sourcing From
                </Label>
                <MultiSelectDropdown
                  placeholder="Select countries to source from"
                  options={sourcingFromOptions}
                  selectedValues={formData.sourcing_from}
                  onSelectionChange={(value) => handleDropdownSelect('sourcing_from', value)}
                  isDataLoading={loadingCountries}
                  id="sourcing-from"
                />
              </div>

              {/* Use Case - Checkbox Group */}
              <CheckboxGroup
                label="Use Case"
                options={useCases.map((option) => option.name)}
                selectedValues={formData.use_case.map((option) => option.name)}
                onSelectionChange={(name) => {
                  const option = useCases.find((opt) => opt.name === name);
                  if (option) {
                    const isCurrentlySelected = formData.use_case.some(
                      (selected) => selected.id === option.id,
                    );
                    handleCheckboxChange('use_case', option, !isCurrentlySelected);
                  }
                }}
                idPrefix="use-case"
              />

              {/* Product Categories - Checkbox Group */}
              <CheckboxGroup
                label="Product Categories"
                options={productCategories}
                selectedValues={formData.product_categories}
                onSelectionChange={(category) => {
                  const isCurrentlySelected = formData.product_categories.includes(category);
                  handleProductCategoryChange(category, !isCurrentlySelected);
                }}
                idPrefix="category"
              />

              {/* Compliance Requirements - Checkbox Group */}
              <CheckboxGroup
                label="Compliance Requirements"
                options={complianceOptions.map((option) => option.name)}
                selectedValues={formData.compliance_reqs.map((option) => option.name)}
                onSelectionChange={(name) => {
                  const option = complianceOptions.find((opt) => opt.name === name);
                  if (option) {
                    const isCurrentlySelected = formData.compliance_reqs.some(
                      (selected) => selected.id === option.id,
                    );
                    handleCheckboxChange('compliance_reqs', option, !isCurrentlySelected);
                  }
                }}
                gridCols="grid-cols-3"
                idPrefix="compliance"
              />

              {/* Preferred Shipping Term - Single Select Checkboxes */}
              <div className="space-y-2">
                <Label className="text-base font-medium">Preferred Shipping Term</Label>
                <div className="grid grid-cols-3 gap-2">
                  {shippingTerms.map((option) => (
                    <div key={option.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`shipping-term-${option.id}`}
                        checked={formData.pref_shipping_term?.id === option.id}
                        onCheckedChange={(checked) =>
                          handleShippingTermChange(option.id, checked === true)
                        }
                      />
                      <Label
                        htmlFor={`shipping-term-${option.id}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {option.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Target Lead Time - Multi Select (styled like single select) */}
              <div className="space-y-2">
                <Label htmlFor="lead-time" className="text-base font-medium">
                  Target Lead Time
                </Label>
                <Select onValueChange={handleLeadTimeSelect}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select lead time">
                      {formData.target_lead_time.length > 0
                        ? formData.target_lead_time.map((opt) => opt.name).join(', ')
                        : 'Select lead time'}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {leadTimeOptions.map((time) => (
                      <SelectItem key={time.id} value={time.name}>
                        {time.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Submit Button */}
              <div className="pt-4">
                <Button type="submit" className="w-full">
                  Save Sourcing Preferences
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default withBuyerRoute(SourcingIntentForm);
