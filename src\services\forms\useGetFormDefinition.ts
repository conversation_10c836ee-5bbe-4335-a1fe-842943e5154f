import apiClient from '@/lib/api-client';
import { handleApiError } from '@/lib/error-handler';
import { queryKeys } from '@/lib/query-keys';
import type { FormioFormResponse, FormioSchema } from '@/utils/formio/types';
import { useQuery } from '@tanstack/react-query';

export async function getFormDefinition(keyword: string): Promise<FormioSchema | undefined> {
  try {
    const response = await apiClient.get<FormioSchema>(`/api/v1/forms/${keyword}`);
    return response.data;
  } catch (error) {
    throw handleApiError(error, 'Failed to fetch form definition');
  }
}

export const useGetFormDefinition = (keyword: string) => {
  return useQuery<FormioFormResponse | undefined>({
    queryKey: queryKeys.accountBasics.formDefinition(keyword),
    queryFn: () => getFormDefinition(keyword) as Promise<FormioFormResponse | undefined>,
    enabled: !!keyword,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};
