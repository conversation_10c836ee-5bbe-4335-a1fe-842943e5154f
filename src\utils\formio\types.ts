export interface FormioFileConfig {
  name: string;
  url: string;
  options?: {
    withCredentials?: boolean;
    [key: string]: any;
  };
}

export interface FormioFileType {
  label: string;
  value: string;
}

export interface FormioComponent {
  disabled: boolean | undefined;
  key?: string;
  type: string;
  input?: boolean;
  label?: string;
  placeholder?: string;
  description?: string;
  defaultValue?: any;
  values?: Array<{ label: string; value: string }>;
  data?: {
    values?: Array<{ label: string; value: string }>;
  };
  dataSrc?: string;
  searchEnabled?: boolean;
  properties?: Record<string, any>;
  ext?: Record<string, any>;
  validate?: {
    required?: boolean;
    pattern?: string;
    patternMessage?: string;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    onlyAvailableItems?: boolean;
  };
  components?: FormioComponent[];
  file?: FormioFileConfig;
  fileTypes?: FormioFileType[];
  rows?: number;
  format?: string;
  enableDate?: boolean;
  enableTime?: boolean;
}

export interface FormioColumn {
  pull: number;
  push: number;
  size: string;
  width: number;
  offset: number;
  components: FormioComponent[];
}

export interface FormioColumnsComponent {
  type: 'columns';
  columns: FormioColumn[];
  properties?: Record<string, any>;
}

export interface FormioFormResponse {
  id: number;
  schema: FormioSchema;
  keyword: string;
}

export interface FormioSchema {
  type: 'form';
  title: string;
  components: Array<FormioComponent | FormioColumnsComponent>;
}
