'use client';

import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardContent,
  But<PERSON>,
} from '@kratex-tradetech/kratex-ui-library';
import { Edit, User } from 'lucide-react';

interface PersonalInfoData {
  full_name: string;
  email: string;
  phone_number: string;
  state: string;
  country: string;
}

interface PersonalInfoProps {
  data: PersonalInfoData;
  onEdit: (section: string) => void;
}

const PersonalInfo: React.FC<PersonalInfoProps> = ({ data, onEdit }) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <User className="h-4 w-4" />
            Personal Information
          </CardTitle>
          <Button variant="outline" size="sm" onClick={() => onEdit('personal')}>
            <Edit className="h-3 w-3 mr-1" />
            Edit
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium text-gray-600">Full Name</p>
            <p className="text-sm">{data.full_name}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Email</p>
            <p className="text-sm">{data.email}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Phone Number</p>
            <p className="text-sm">{data.phone_number}</p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-600">Location</p>
            <p className="text-sm">
              {data.state}, {data.country}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonalInfo;
