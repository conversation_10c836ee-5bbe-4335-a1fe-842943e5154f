# 🧱 Kratex Web App

```markdown
Kratex is an innovative platform designed to facilitate seamless interactions among various
stakeholders in the construction supply chain, including Buyers, Epod users, Kretex, Suppliers,
Agents, and Kretex Admin. The platform is engineered to streamline the procurement process for
construction goods through advanced technological solutions.
```

## ⚙️ Project Setup

Follow these steps to set up the project locally:

```bash
# 1️⃣ Clone the repository
<NAME_EMAIL>:KrateX-Tradetech/kratex-fe-next.git

# 2️⃣ Navigate into the project directory
cd kratex-fe-next

# 3️⃣ Configure .npmrc for private UI library packages
echo "@kratex-tradetech:registry=https://npm.pkg.github.com/" > .npmrc
echo "//npm.pkg.github.com/:_authToken=YOUR_GITHUB_TOKEN_HERE" >> .npmrc

# 4️⃣ Install dependencies using pnpm
pnpm install

# 5️⃣ Create a local environment file & update variables as needed
.env.local

# 6️⃣ Run the development server
pnpm dev
```

---

## 📁 Folder Structure

```markdown
src/
├── app/ # App Router files (routes, layouts)
├── components/ # Reusable UI components
├── hooks/ # Custom React hooks
├── providers/ # Higher order components
├── services/ # API service functions
├── store/ # Zustand stores
├── styles/ # Global styles and Tailwind config
├── types/ # Global TypeScript types and interfaces
├── utils/ # Utility functions and libraries
```

---

## 🔢 Import Statement Order

```markdown
All import statements should follow this sequence:

1. Third-party modules/libraries
   Example: `react`, `next`, `axios`, `zustand`, `@tanstack/react-query`
2. Custom modules/components\*\*  
   From: `@/components/...`
3. Services  
   From: `@/services/...`
4. Store
   From: `@/store/...`
5. lib/utils
   From: `@/lib/...` or `@/utils/...`
6. CSS/Styles
   Only global or module-specific styles

// ✅ Correct Order Example
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { fetchUser } from "@/services/user.service";
import { useUserStore } from "@/store/user.store";
import { formatDate } from "@/lib/date";
import "@/styles/globals.css";
```

---

## 🧩 Naming Conventions

### 1. **Components**

- PascalCase for files and folders
- Folder can have `index.tsx` if the component is default exported

```
components/
├── Header/
│   └── index.tsx
├── UserCard.tsx
```

### 2. **Pages (App Router)**

- Use lowercase folder names
- Route files should be named `page.tsx`
- Layouts should be named `layout.tsx`

```
app/
├── dashboard/
│   ├── layout.tsx
│   └── page.tsx
```

### 3. **Services**

- camelCase function names
- File names should be descriptive and suffixed with `.service.ts`

```
services/
├── user.service.ts
├── auth.service.ts
```

### 4. **Types**

- PascalCase type/interface names
- File names can match entity name and be suffixed with `.types.ts`

```
types/
├── user.types.ts
// Inside
export interface UserProfile { ... }
```

### 5. **Store**

- camelCase store name, suffixed with `.store.ts`
- Use Zustand or preferred state manager

```
store/
├── auth.store.ts
├── user.store.ts
```

### 6. **Custom Hooks / Utils**

- Hooks: `useXyz.ts`
- Utils: camelCase functions and file names

```
hooks/
├── useDebounce.ts

lib/
├── formatDate.ts
```

### 7. **Folders**

- All folder names should be **kebab-case** except `components` which follows PascalCase for co-location
- For reusable modules, prefer feature-based structure

---

## 💡 Additional Best Practices

### ✅ Code Quality

- Use **TypeScript** strictly — no `any`, use generics where needed.
- Enable `strict` mode in `tsconfig.json`.
- Prefer `const` over `let` unless reassignment is necessary.
- Avoid inline styles — use **Tailwind CSS** or `className`.

### 🧪 Testing

- Use [Vitest](https://vitest.dev/) + [React Testing Library](https://testing-library.com/)
- Test all shared logic, services, and components.

### 🔍 Linting and Formatting

- Use ESLint + Prettier with strict rules.
- Enforce consistent commit messages with `commitlint`.

### 📦 Dependency Management

- Lock dependencies using `package-lock.json` or `pnpm-lock.yaml`.
- Use `npm audit` or `pnpm audit` regularly.

### 🔁 State Management

- Prefer **Zustand** for simplicity.
- Keep global state minimal and modular.

### 🔐 Environment Variables

- Use `.env.local` for local development.
- Prefix public env variables with `NEXT_PUBLIC_`.

---

## 📦 Scripts

```bash
pnpm dev         # Run development server
pnpm build       # Build for production
pnpm lint        # Lint project
pnpm test        # Run unit tests
pnpm format      # Format code with Prettier
```

---

## 🧪 Tech Stack

- **Next.js 14+ (App Router)**
- **TypeScript**
- **Tailwind CSS**
- **Zustand / React Query**
- **Vitest + Testing Library**
- **ESLint + Prettier + Husky**

---

## 🛠️ Contribution Guidelines

1. Follow import and naming conventions above.
2. Ensure no lint errors.
3. Write tests for new logic.
4. Use semantic commit messages. (Refer: [Semantic commits](https://gist.github.com/joshbuchea/6f47e86d2510bce28f8e7f42ae84c716), [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/))

---
